import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { api } from '@/lib/api';
import { User, LoginCredentials, AuthState } from '@/types';
import config from '@/config';

interface AuthStore extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  loadUser: () => Promise<void>;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials: LoginCredentials) => {
        console.log('Auth store: Starting login process');
        set({ isLoading: true });
        try {
          console.log('Auth store: Making API call');
          const response = await api.auth.login(credentials);
          console.log('Auth store: API response received:', response.data);
          const { user, token, role } = response.data.data;
          
          // Store token in localStorage
          localStorage.setItem(config.auth.tokenKey, token);
          console.log('Auth store: Token stored in localStorage');
          
          // Add role to user object if not present
          const userWithRole = { ...user, role: user.role || role };
          
          const newState = {
            user: userWithRole,
            token,
            isAuthenticated: true,
            isLoading: false,
          };
          
          set(newState);
          console.log('Auth store: State updated successfully', newState);
          
          // Force a state check after update
          const currentState = get();
          console.log('Auth store: Current state after set:', currentState);
        } catch (error) {
          console.error('Auth store: Login failed', error);
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        localStorage.removeItem(config.auth.tokenKey);
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      loadUser: async () => {
        const token = localStorage.getItem(config.auth.tokenKey);
        if (!token) return;

        set({ isLoading: true });
        try {
          const response = await api.auth.getProfile();
          const user = response.data.data.user;
          
          // Ensure user has role field, default to 'member' if not present
          const userWithRole = { ...user, role: user.role || 'member' };
          
          set({
            user: userWithRole,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch {
          // Token is invalid, clear it
          localStorage.removeItem(config.auth.tokenKey);
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      setUser: (user: User | null) => {
        set({ user, isAuthenticated: !!user });
      },

      setToken: (token: string | null) => {
        set({ token });
        if (token) {
          localStorage.setItem(config.auth.tokenKey, token);
        } else {
          localStorage.removeItem(config.auth.tokenKey);
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      initialize: async () => {
        console.log('Auth store: Initializing');
        const token = localStorage.getItem(config.auth.tokenKey);
        const storedState = localStorage.getItem('auth-store');
        
        console.log('Auth store: Found token:', !!token);
        console.log('Auth store: Found stored state:', !!storedState);
        
        if (!token) {
          console.log('Auth store: No token found in localStorage');
          set({ isLoading: false }); // Make sure loading is false
          return;
        }
        
        // If we have stored state, try to restore it first
        if (storedState) {
          try {
            const parsed = JSON.parse(storedState);
            if (parsed.state && parsed.state.user && parsed.state.isAuthenticated) {
              console.log('Auth store: Restoring auth state from localStorage');
              
              // Ensure user has role field, default to 'member' if not present
              const userWithRole = { 
                ...parsed.state.user, 
                role: parsed.state.user.role || 'member' 
              };
              
              set({
                user: userWithRole,
                token: parsed.state.token || token,
                isAuthenticated: parsed.state.isAuthenticated,
                isLoading: false
              });
              return;
            }
          } catch (error) {
            console.error('Auth store: Failed to parse stored state:', error);
          }
        }
        
        console.log('Auth store: Token found, loading user profile');
        try {
          set({ isLoading: true });
          await useAuthStore.getState().loadUser();
        } catch (error) {
          console.error('Auth store: Failed to load user during initialization', error);
          // Clear invalid token
          localStorage.removeItem(config.auth.tokenKey);
          localStorage.removeItem('auth-store');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
); 