'use client';

import React from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SubmoduleFormData {
  name: string;
  description: string;
  status: 'active' | 'inactive';
  systemPrompt: string;
}

interface SubmoduleModalProps {
  isOpen: boolean;
  isEdit: boolean;
  onClose: () => void;
  formState: Omit<SubmoduleFormData, 'id'>;
  onFormChange: (field: keyof Omit<SubmoduleFormData, 'id'>, value: string) => void;
  onSubmit: () => void;
}

const SubmoduleModal: React.FC<SubmoduleModalProps> = ({
  isOpen,
  isEdit,
  onClose,
  formState,
  onFormChange,
  onSubmit,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEdit ? "Edit Submodule" : "Create Submodule"}>
      <div className="space-y-4">
        <Input
          label="Submodule Name"
          placeholder="Enter submodule name"
          value={formState.name}
          onChange={(e) => onFormChange('name', e.target.value)}
        />
        <Input
          label="Description"
          placeholder="Enter submodule description"
          value={formState.description}
          onChange={(e) => onFormChange('description', e.target.value)}
        />
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <Select
            value={formState.status}
            onValueChange={(value) => onFormChange('status', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <textarea
          value={formState.systemPrompt}
          onChange={(e) => onFormChange('systemPrompt', e.target.value)}
          placeholder="Enter the system prompt for this submodule..."
          rows={10}
          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-mono"
        />
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={onSubmit}>{isEdit ? "Update" : "Create"}</Button>
        </div>
      </div>
    </Modal>
  );
};

export default SubmoduleModal;