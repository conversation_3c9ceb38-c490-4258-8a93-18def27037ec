'use client';

import { Inter } from 'next/font/google'
import { useEffect } from 'react'
import { useAuthStore } from '@/store/auth'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { initialize } = useAuthStore();

  useEffect(() => {
    // Initialize auth state on app startup
    console.log('Root layout: Initializing auth store');
    initialize().then(() => {
      console.log('Root layout: Auth store initialization completed');
    }).catch((error) => {
      console.error('Root layout: Auth store initialization failed:', error);
    });
  }, [initialize]);

  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
