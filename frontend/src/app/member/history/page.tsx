'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuthStore } from '@/store/auth';
import { api } from '@/lib/api';
import { Trainer } from '@/types';
import { 
  History, 
  MessageCircle, 
  Calendar, 
  Clock,
  Search,
  Eye,
  Download,
} from 'lucide-react';

interface ConversationHistory {
  id: number;
  trainer: Trainer;
  startedAt: string;
  lastMessageAt: string;
  messageCount: number;
  status: 'active' | 'completed';
  submodule?: string;
  score?: number;
  summary?: string;
}

export default function MemberHistoryPage() {
  const { user } = useAuthStore();
  const [conversations, setConversations] = useState<ConversationHistory[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<ConversationHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTrainer, setSelectedTrainer] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [trainers, setTrainers] = useState<Trainer[]>([]);


  const fetchTrainers = useCallback(async () => {
    try {
      if (user?.id) {
        const response = await api.members.getAssignedTrainers();
        // Handle the correct response structure: response.data.data.trainers
        setTrainers(response.data.data?.trainers || []);
      }
    } catch (error) {
      console.error('Error fetching trainers:', error);
    }
  }, [user?.id]);

  const fetchConversationHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Mock conversation history data (TODO: Replace with actual API)
      const mockConversations: ConversationHistory[] = [
        {
          id: 1,
          trainer: {
            id: 1,
            adminId: 1,
            name: 'JavaScript Trainer',
            systemPrompt: '',
            description: 'Learn JavaScript fundamentals',
            status: 'active',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          },
          startedAt: '2024-01-15T10:00:00Z',
          lastMessageAt: '2024-01-15T11:30:00Z',
          messageCount: 25,
          status: 'completed',
          submodule: 'Variables and Data Types',
          score: 85,
          summary: 'Discussed JavaScript variables, data types, and basic operations'
        },
        {
          id: 2,
          trainer: {
            id: 1,
            adminId: 1,
            name: 'JavaScript Trainer',
            systemPrompt: '',
            description: 'Learn JavaScript fundamentals',
            status: 'active',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          },
          startedAt: '2024-01-14T14:00:00Z',
          lastMessageAt: '2024-01-14T15:45:00Z',
          messageCount: 18,
          status: 'completed',
          submodule: 'Functions and Scope',
          score: 92,
          summary: 'Covered function declarations, expressions, and scope concepts'
        },
        {
          id: 3,
          trainer: {
            id: 2,
            adminId: 1,
            name: 'Python Trainer',
            systemPrompt: '',
            description: 'Learn Python programming',
            status: 'active',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          },
          startedAt: '2024-01-13T09:00:00Z',
          lastMessageAt: '2024-01-13T10:15:00Z',
          messageCount: 12,
          status: 'active',
          submodule: 'Introduction to Python',
          summary: 'Getting started with Python syntax and basic concepts'
        }
      ];
      
      setConversations(mockConversations);
      
    } catch (error) {
      console.error('Error fetching conversation history:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const filterConversations = useCallback(() => {
    let filtered = conversations;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(conv =>
        conv.trainer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.submodule?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conv.summary?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by trainer
    if (selectedTrainer !== 'all') {
      filtered = filtered.filter(conv => conv.trainer.id.toString() === selectedTrainer);
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(conv => conv.status === selectedStatus);
    }

    setFilteredConversations(filtered);
  }, [conversations, searchTerm, selectedTrainer, selectedStatus]);

  useEffect(() => {
    fetchConversationHistory();
    fetchTrainers();
  }, [fetchConversationHistory, fetchTrainers]);

  useEffect(() => {
    filterConversations();
  }, [filterConversations]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    if (score >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  const handleViewConversation = (conversationId: number) => {
    // TODO: Navigate to conversation detail or open modal
    console.log('View conversation:', conversationId);
  };

  const handleExportHistory = () => {
    // TODO: Implement export functionality
    console.log('Export conversation history');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading conversation history...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Conversation History</h1>
            <p className="text-purple-100">Review your past conversations and learning progress</p>
          </div>
          <button
            onClick={handleExportHistory}
            className="flex items-center space-x-2 bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Conversations</p>
              <p className="text-2xl font-bold text-gray-900">{conversations.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Clock className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {conversations.filter(c => c.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <MessageCircle className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Messages</p>
              <p className="text-2xl font-bold text-gray-900">
                {conversations.reduce((sum, c) => sum + c.messageCount, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <History className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {conversations.filter(c => c.score).length > 0 
                  ? (conversations.reduce((sum, c) => sum + (c.score || 0), 0) / conversations.filter(c => c.score).length).toFixed(1)
                  : '0.0'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Trainer</label>
            <select
              value={selectedTrainer}
              onChange={(e) => setSelectedTrainer(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Trainers</option>
              {trainers.map((trainer) => (
                <option key={trainer.id} value={trainer.id.toString()}>
                  {trainer.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedTrainer('all');
                setSelectedStatus('all');
              }}
              className="w-full px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Conversation List */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Conversations ({filteredConversations.length})
          </h2>
        </div>
        <div className="p-6">
          {filteredConversations.length > 0 ? (
            <div className="space-y-4">
              {filteredConversations.map((conversation) => (
                <div key={conversation.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {conversation.trainer.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{conversation.trainer.name}</h3>
                        <p className="text-sm text-gray-500">{conversation.submodule}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(conversation.status)}`}>
                        {conversation.status}
                      </span>
                      {conversation.score && (
                        <span className={`text-sm font-medium ${getScoreColor(conversation.score)}`}>
                          {conversation.score}%
                        </span>
                      )}
                      <button
                        onClick={() => handleViewConversation(conversation.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-sm text-gray-600">{conversation.summary}</p>
                  </div>
                  
                  <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        Started: {formatDate(conversation.startedAt)}
                      </span>
                      <span className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Last: {formatTime(conversation.lastMessageAt)}
                      </span>
                      <span className="flex items-center">
                        <MessageCircle className="h-3 w-3 mr-1" />
                        {conversation.messageCount} messages
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No conversations found</p>
              <p className="text-sm text-gray-400">
                {searchTerm || selectedTrainer !== 'all' || selectedStatus !== 'all' 
                  ? 'Try adjusting your filters'
                  : 'Start chatting with your trainers to see history here'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}