'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  MessageCircle, 
  TrendingUp, 
  History, 
  User, 
  LogOut,
  Menu,
  X,
  Bell
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/member/dashboard', icon: Home },
  { name: 'Chat', href: '/member/chat', icon: MessageCircle },
  { name: 'Progress', href: '/member/progress', icon: TrendingUp },
  { name: 'History', href: '/member/history', icon: History },
  { name: 'Profile', href: '/member/profile', icon: User },
];

export default function MemberLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isAuthenticated, logout, isLoading } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    // Don't redirect during SSR or hydration
    if (!isHydrated) {
      console.log('Member layout: Waiting for hydration');
      return;
    }
    
    // Don't redirect while loading
    if (isLoading) {
      console.log('Member layout: Auth is loading');
      return;
    }

    // Mark that we've checked auth at least once
    setHasCheckedAuth(true);
    
    // Check for auth token in localStorage as fallback
    const token = localStorage.getItem('auth_token');
    const storedAuthState = localStorage.getItem('auth-store');
    
    const userRole = user?.role || 'member';
    console.log('Member layout auth check:', {
      isAuthenticated,
      user: user ? { id: user.id, role: userRole, name: user.name } : null,
      hasToken: !!token,
      hasStoredAuth: !!storedAuthState,
      pathname,
      isLoading,
      isHydrated,
      hasCheckedAuth
    });
    
    // Debug localStorage content
    if (token) {
      console.log('Member layout: Token found:', token.substring(0, 50) + '...');
    }
    if (storedAuthState) {
      try {
        const parsed = JSON.parse(storedAuthState);
        console.log('Member layout: Stored auth state:', {
          hasUser: !!parsed.state?.user,
          isAuthenticated: parsed.state?.isAuthenticated,
          userRole: parsed.state?.user?.role
        });
      } catch (e) {
        console.log('Member layout: Failed to parse stored auth state');
      }
    }
    
    // If we have a token but no auth state, give it some time to load
    if (token && !isAuthenticated && !user) {
      console.log('Member layout: Have token but no auth state, waiting...');
      return;
    }
    
    if (!isAuthenticated && !token) {
      console.log('Member layout: Not authenticated and no token, redirecting to login');
      window.location.href = '/auth/login';
      return;
    }

    // For members, role is always 'member' or might not be present in user object
    // Members don't have role field in database, it's implied from JWT token
    if (isAuthenticated && user && userRole !== 'member') {
      console.log('Member layout: User role is not member:', userRole);
      window.location.href = '/auth/login';
      return;
    }
    
    console.log('Member layout: Auth check passed - user is authenticated member');
  }, [isAuthenticated, user, router, isLoading, isHydrated, pathname]);

  const handleLogout = () => {
    logout();
    window.location.href = '/auth/login';
  };

  // Show loading during SSR/hydration or if we haven't checked auth yet
  if (!isHydrated || isLoading || (!hasCheckedAuth && !isAuthenticated)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Check for token as fallback if auth state not ready
  const token = localStorage.getItem('auth_token');
  if (!isAuthenticated && !token) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex flex-col w-64 bg-white shadow-xl">
          <div className="flex items-center justify-between h-16 px-4 bg-blue-600">
            <h1 className="text-xl font-semibold text-white">Member Portal</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-white hover:text-gray-200"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
          <div className="p-4 border-t">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col bg-white shadow-sm">
          <div className="flex items-center h-16 px-4 bg-blue-600">
            <h1 className="text-xl font-semibold text-white">Member Portal</h1>
          </div>
          <nav className="flex-1 px-4 py-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
          <div className="p-4 border-t">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-white shadow-sm">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-gray-500 hover:text-gray-700"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-700">
                  {user?.name}
                </span>
              </div>
              
              <button className="text-gray-500 hover:text-gray-700">
                <Bell className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-4">
          {children}
        </main>
      </div>
    </div>
  );
} 