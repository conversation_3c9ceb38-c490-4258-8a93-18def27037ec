'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import Link from 'next/link';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SubmoduleCreationList from '@/components/admin/trainers/SubmoduleCreationList';
import SubmoduleModal from '@/components/admin/trainers/SubmoduleModal';

interface TrainerFormData {
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
}

interface SubmoduleFormData {
  id: string; // temporary ID for frontend management
  name: string;
  description: string;
  status: 'active' | 'inactive';
  systemPrompt: string;
}

const CreateTrainerPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<TrainerFormData>({
    name: '',
    description: '',
    status: 'draft'
  });
  const [errors, setErrors] = useState<Partial<TrainerFormData>>({});

  // Submodule management state
  const [submodules, setSubmodules] = useState<SubmoduleFormData[]>([]);
  const [submoduleModal, setSubmoduleModal] = useState({
    isOpen: false,
    isEdit: false,
    editingId: null as string | null
  });
  const [submoduleForm, setSubmoduleForm] = useState<Omit<SubmoduleFormData, 'id'>>({
    name: '',
    description: '',
    status: 'active',
    systemPrompt: ''
  });

  const handleInputChange = (field: keyof TrainerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Submodule management functions
  const generateSubmoduleId = () => `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const openSubmoduleModal = (submodule?: SubmoduleFormData) => {
    if (submodule) {
      setSubmoduleForm({
        name: submodule.name,
        description: submodule.description,
        status: submodule.status,
        systemPrompt: submodule.systemPrompt
      });
      setSubmoduleModal({
        isOpen: true,
        isEdit: true,
        editingId: submodule.id
      });
    } else {
      setSubmoduleForm({
        name: '',
        description: '',
        status: 'active',
        systemPrompt: ''
      });
      setSubmoduleModal({
        isOpen: true,
        isEdit: false,
        editingId: null
      });
    }
  };

  const closeSubmoduleModal = () => {
    setSubmoduleModal({
      isOpen: false,
      isEdit: false,
      editingId: null
    });
    setSubmoduleForm({
      name: '',
      description: '',
      status: 'active',
      systemPrompt: ''
    });
  };

  const handleSubmoduleFormChange = (field: keyof Omit<SubmoduleFormData, 'id'>, value: string) => {
    setSubmoduleForm(prev => ({ ...prev, [field]: value }));
  };

  const handleCreateSubmodule = () => {
    const newSubmodule: SubmoduleFormData = {
      id: generateSubmoduleId(),
      ...submoduleForm
    };
    setSubmodules(prev => [...prev, newSubmodule]);
    closeSubmoduleModal();
  };

  const handleUpdateSubmodule = () => {
    if (!submoduleModal.editingId) return;

    setSubmodules(prev => prev.map(sub =>
      sub.id === submoduleModal.editingId
        ? { ...sub, ...submoduleForm }
        : sub
    ));
    closeSubmoduleModal();
  };

  const handleDeleteSubmodule = (submoduleId: string) => {
    setSubmodules(prev => prev.filter(sub => sub.id !== submoduleId));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<TrainerFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Name must be at least 3 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Create the trainer first
      const trainerResponse = await api.trainers.create({ ...formData, systemPrompt: 'default' });
      const trainerId = trainerResponse.data.trainer.id;

      // Create submodules if any exist
      if (submodules.length > 0) {
        for (const submodule of submodules) {
          await api.submodules.create(trainerId, {
            name: submodule.name,
            systemPrompt: submodule.systemPrompt,
            description: submodule.description,
            status: submodule.status
          });
        }
      }

      router.push('/admin/dashboard/trainers');
    } catch (error: unknown) {
      console.error('Error creating trainer:', error);
      // Handle specific API errors
      if (error instanceof Error) {
        // A more robust error handling could be implemented here
        // For now, just log the message
        console.error(error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/dashboard/trainers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Trainers
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Trainer</h1>
            <p className="text-gray-600">Set up a new AI trainer with custom configuration</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Input
                label="Trainer Name"
                placeholder="Enter trainer name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={errors.name}
                helperText="A descriptive name for your AI trainer"
              />
            </div>

            <div className="md:col-span-2">
              <Input
                label="Description"
                placeholder="Enter trainer description (optional)"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                error={errors.description}
                helperText="Brief description of what this trainer does"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500 mt-1">
                Draft trainers are not available to members
              </p>
            </div>
          </div>
        </div>

        {/* Submodules Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Submodules</h2>
              <p className="text-sm text-gray-600">Add submodules to organize your trainer's functionality</p>
            </div>
          </div>

          <SubmoduleCreationList
            submodules={submodules}
            onOpenSubmoduleModal={openSubmoduleModal}
            onDeleteSubmodule={handleDeleteSubmodule}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Link href="/admin/dashboard/trainers">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" loading={loading}>
            <Save className="h-4 w-4 mr-2" />
            Create Trainer
          </Button>
        </div>
      </form>

      {/* Submodule Modal */}
      <SubmoduleModal
        isOpen={submoduleModal.isOpen}
        isEdit={submoduleModal.isEdit}
        onClose={closeSubmoduleModal}
        formState={submoduleForm}
        onFormChange={handleSubmoduleFormChange}
        onSubmit={submoduleModal.isEdit ? handleUpdateSubmodule : handleCreateSubmodule}
      />
    </div>
  );
};

export default CreateTrainerPage;