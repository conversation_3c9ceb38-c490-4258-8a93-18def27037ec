'use client';

import { useAuthStore } from '@/store/auth';
import { api } from '@/lib/api';
import { useState, useEffect } from 'react';

export default function TestAuth() {
  const authState = useAuthStore();
  const [loginStatus, setLoginStatus] = useState('');
  const [isClient, setIsClient] = useState(false);
  const [localStorageData, setLocalStorageData] = useState<{
    token: string | null;
    authStore: string | null;
  }>({
    token: null,
    authStore: null
  });

  // Handle client-side rendering
  useEffect(() => {
    setIsClient(true);
    
    // Update localStorage data
    const updateLocalStorageData = () => {
      setLocalStorageData({
        token: localStorage.getItem('auth_token'),
        authStore: localStorage.getItem('auth-store')
      });
    };
    
    updateLocalStorageData();
    
    // Set up interval to refresh localStorage data
    const interval = setInterval(updateLocalStorageData, 1000);
    return () => clearInterval(interval);
  }, []);

  const testLogin = async () => {
    try {
      setLoginStatus('Logging in...');
      
      // Clear any existing auth data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth-store');
      
      console.log('Test: Starting login');
      const response = await api.auth.login({
        email: '<EMAIL>',
        password: '123456',
        userType: 'member'
      });
      
      console.log('Test: Login API response:', response.data);
      
      const { user, token } = response.data.data;
      
      // Manually set auth state
      authState.setUser(user);
      authState.setToken(token);
      
      console.log('Test: Auth state after manual set:', useAuthStore.getState());
      
      setLoginStatus('Login successful! Check console for details.');
      
    } catch (error: unknown) {
      console.error('Test: Login failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setLoginStatus('Login failed: ' + errorMessage);
    }
  };

  const clearAuth = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth-store');
    authState.logout();
    setLoginStatus('Auth cleared');
  };

  const navigateToDashboard = () => {
    window.location.href = '/member/dashboard';
  };

  // Show loading until client-side hydration
  if (!isClient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-2xl font-bold">Auth Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Auth State */}
          <div className="bg-gray-100 p-4 rounded">
            <h2 className="font-semibold mb-2">Current Auth State</h2>
            <pre className="text-xs overflow-auto bg-white p-2 rounded border max-h-64">
              {JSON.stringify(authState, null, 2)}
            </pre>
          </div>

          {/* localStorage */}
          <div className="bg-gray-100 p-4 rounded">
            <h2 className="font-semibold mb-2">localStorage</h2>
            <div className="text-xs space-y-1">
              <p><strong>auth_token:</strong> {localStorageData.token ? 'Present' : 'None'}</p>
              <p><strong>auth-store:</strong> {localStorageData.authStore ? 'Present' : 'None'}</p>
              
              {localStorageData.token && (
                <div className="mt-2">
                  <p><strong>Token (first 50 chars):</strong></p>
                  <p className="break-all bg-white p-1 rounded">{localStorageData.token.substring(0, 50)}...</p>
                </div>
              )}
              
              {localStorageData.authStore && (
                <div className="mt-2">
                  <p><strong>Stored State:</strong></p>
                  <pre className="bg-white p-1 rounded text-xs overflow-auto max-h-32">
                    {JSON.stringify(JSON.parse(localStorageData.authStore), null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-4">
          <div className="flex space-x-4 flex-wrap gap-2">
            <button 
              onClick={testLogin}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Test Login (<EMAIL>)
            </button>
            
            <button 
              onClick={clearAuth}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Clear Auth
            </button>
            
            <button 
              onClick={navigateToDashboard}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Go to Member Dashboard
            </button>
          </div>
          
          {loginStatus && (
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded">
              <p className="text-sm">{loginStatus}</p>
            </div>
          )}
        </div>

        {/* Links */}
        <div className="space-y-2">
          <p><a href="/auth/login" className="text-blue-600 hover:underline">Go to Login Page</a></p>
          <p><a href="/member/dashboard" className="text-blue-600 hover:underline">Go to Member Dashboard</a></p>
          <p><a href="/" className="text-blue-600 hover:underline">Go to Home</a></p>
        </div>
        
        {/* Debug Info */}
        <div className="bg-blue-50 border border-blue-200 p-3 rounded">
          <h3 className="font-semibold mb-2">Debug Info</h3>
          <div className="text-xs space-y-1">
            <p><strong>isClient:</strong> {isClient.toString()}</p>
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'SSR'}</p>
            <p><strong>User Agent:</strong> {typeof navigator !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'SSR'}</p>
          </div>
        </div>
      </div>
    </div>
  );
} 