'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/auth'

export default function Home() {
  const { user, isAuthenticated, isLoading } = useAuthStore()
  const [isHydrated, setIsHydrated] = useState(false)
  const [hasRedirected, setHasRedirected] = useState(false)

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  useEffect(() => {
    // Don't redirect during SSR or hydration
    if (!isHydrated) {
      console.log('Home page: Waiting for hydration');
      return
    }
    
    // Don't redirect while loading or if we already redirected
    if (isLoading || hasRedirected) {
      console.log('Home page: Still loading auth state or already redirected');
      return;
    }
    
    // Check for token as fallback
    const token = localStorage.getItem('auth_token');
    
    console.log('Home page auth check:', {
      isAuthenticated,
      user: user ? { id: user.id, role: user.role } : null,
      hasToken: !!token
    });
    
    // If we have a token but no user data, wait for auth to load
    if (token && !user && !isAuthenticated) {
      console.log('Home page: Has token but no user data, waiting for auth to load');
      return;
    }
    
    if (!isAuthenticated && !token) {
      console.log('Home page: Not authenticated, redirecting to login');
      setHasRedirected(true);
      window.location.href = '/auth/login'
      return
    }

    // Redirect based on user role
    if (user?.role === 'member') {
      console.log('Home page: Member user, redirecting to member dashboard');
      setHasRedirected(true);
      window.location.href = '/member/dashboard'
    } else if (user?.role === 'admin' || user?.role === 'super_admin') {
      console.log('Home page: Admin user, redirecting to admin dashboard');
      setHasRedirected(true);
      window.location.href = '/admin/dashboard'
    } else if (token && !user) {
      // If we have token but no user, wait a bit more
      console.log('Home page: Has token but no user, will redirect after auth loads');
    } else {
      console.log('Home page: Unknown role, redirecting to login');
      setHasRedirected(true);
      window.location.href = '/auth/login'
    }
  }, [isAuthenticated, user, isLoading, isHydrated, hasRedirected])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{isLoading ? 'Authenticating...' : 'Loading...'}</p>
      </div>
    </div>
  )
}
