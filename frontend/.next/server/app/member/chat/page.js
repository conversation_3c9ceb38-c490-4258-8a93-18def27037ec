/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/member/chat/page";
exports.ids = ["app/member/chat/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmember%2Fchat%2Fpage&page=%2Fmember%2Fchat%2Fpage&appPaths=%2Fmember%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fmember%2Fchat%2Fpage.tsx&appDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmember%2Fchat%2Fpage&page=%2Fmember%2Fchat%2Fpage&appPaths=%2Fmember%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fmember%2Fchat%2Fpage.tsx&appDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/layout.tsx */ \"(rsc)/./src/app/member/layout.tsx\"));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/chat/page.tsx */ \"(rsc)/./src/app/member/chat/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'member',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/meet_trainer/frontend/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/member/chat/page\",\n        pathname: \"/member/chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/member/chat/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmember%2Fchat%2Fpage&page=%2Fmember%2Fchat%2Fpage&appPaths=%2Fmember%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fmember%2Fchat%2Fpage.tsx&appDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBd0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3BpbGUvbWVldF90cmFpbmVyL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/chat/page.tsx */ \"(rsc)/./src/app/member/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZtZW1iZXIlMkZjaGF0JTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFrRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvcGlsZS9tZWV0X3RyYWluZXIvZnJvbnRlbmQvc3JjL2FwcC9tZW1iZXIvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/layout.tsx */ \"(rsc)/./src/app/member/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZtZW1iZXIlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3BpbGUvbWVldF90cmFpbmVyL2Zyb250ZW5kL3NyYy9hcHAvbWVtYmVyL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9ob21lL3BpbGUvbWVldF90cmFpbmVyL2Zyb250ZW5kL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/meet_trainer/frontend/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/meet_trainer/frontend/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/member/chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/member/chat/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/member/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/member/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBd0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3BpbGUvbWVldF90cmFpbmVyL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/chat/page.tsx */ \"(ssr)/./src/app/member/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZtZW1iZXIlMkZjaGF0JTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUFrRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvcGlsZS9tZWV0X3RyYWluZXIvZnJvbnRlbmQvc3JjL2FwcC9tZW1iZXIvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/member/layout.tsx */ \"(ssr)/./src/app/member/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZwaWxlJTJGbWVldF90cmFpbmVyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZtZW1iZXIlMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3BpbGUvbWVldF90cmFpbmVyL2Zyb250ZW5kL3NyYy9hcHAvbWVtYmVyL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp%2Fmember%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"03f52d364998\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9waWxlL21lZXRfdHJhaW5lci9mcm9udGVuZC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDNmNTJkMzY0OTk4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction RootLayout({ children }) {\n    const { initialize } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            // Initialize auth state on app startup\n            console.log('Root layout: Initializing auth store');\n            initialize().then({\n                \"RootLayout.useEffect\": ()=>{\n                    console.log('Root layout: Auth store initialization completed');\n                }\n            }[\"RootLayout.useEffect\"]).catch({\n                \"RootLayout.useEffect\": (error)=>{\n                    console.error('Root layout: Auth store initialization failed:', error);\n                }\n            }[\"RootLayout.useEffect\"]);\n        }\n    }[\"RootLayout.useEffect\"], [\n        initialize\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU9NQTtBQUoyQjtBQUNVO0FBQ3JCO0FBSVAsU0FBU0csV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MsTUFBTSxFQUFFQyxVQUFVLEVBQUUsR0FBR0gseURBQVlBO0lBRW5DRCxnREFBU0E7Z0NBQUM7WUFDUix1Q0FBdUM7WUFDdkNLLFFBQVFDLEdBQUcsQ0FBQztZQUNaRixhQUFhRyxJQUFJO3dDQUFDO29CQUNoQkYsUUFBUUMsR0FBRyxDQUFDO2dCQUNkO3VDQUFHRSxLQUFLO3dDQUFDLENBQUNDO29CQUNSSixRQUFRSSxLQUFLLENBQUMsa0RBQWtEQTtnQkFDbEU7O1FBQ0Y7K0JBQUc7UUFBQ0w7S0FBVztJQUVmLHFCQUNFLDhEQUFDTTtRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXZCwrSkFBZTtzQkFDN0JJOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyIvaG9tZS9waWxlL21lZXRfdHJhaW5lci9mcm9udGVuZC9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmUvYXV0aCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgY29uc3QgeyBpbml0aWFsaXplIH0gPSB1c2VBdXRoU3RvcmUoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZSBvbiBhcHAgc3RhcnR1cFxuICAgIGNvbnNvbGUubG9nKCdSb290IGxheW91dDogSW5pdGlhbGl6aW5nIGF1dGggc3RvcmUnKTtcbiAgICBpbml0aWFsaXplKCkudGhlbigoKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnUm9vdCBsYXlvdXQ6IEF1dGggc3RvcmUgaW5pdGlhbGl6YXRpb24gY29tcGxldGVkJyk7XG4gICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCdSb290IGxheW91dDogQXV0aCBzdG9yZSBpbml0aWFsaXphdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIH0pO1xuICB9LCBbaW5pdGlhbGl6ZV0pO1xuXG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsInVzZUVmZmVjdCIsInVzZUF1dGhTdG9yZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImluaXRpYWxpemUiLCJjb25zb2xlIiwibG9nIiwidGhlbiIsImNhdGNoIiwiZXJyb3IiLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/member/chat/page.tsx":
/*!**************************************!*\
  !*** ./src/app/member/chat/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemberChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(ssr)/./src/hooks/useWebSocket.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bot,Loader2,Send,Star,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction MemberChatPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const trainerId = searchParams.get('trainer');\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const { socket, isConnected, on, off } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_5__.useWebSocket)();\n    const [trainer, setTrainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignedTrainers, setAssignedTrainers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTrainer, setSelectedTrainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionId, setSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [typingIndicator, setTypingIndicator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberChatPage.useEffect\": ()=>{\n            if (trainerId) {\n                setSelectedTrainer(parseInt(trainerId));\n                fetchTrainer(parseInt(trainerId));\n                initializeChat(parseInt(trainerId));\n            }\n        }\n    }[\"MemberChatPage.useEffect\"], [\n        trainerId\n    ]);\n    const fetchAssignedTrainers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MemberChatPage.useCallback[fetchAssignedTrainers]\": async ()=>{\n            try {\n                if (user?.id) {\n                    const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.members.getAssignedTrainers();\n                    // Handle the correct response structure: response.data.data.trainers\n                    setAssignedTrainers(response.data.data?.trainers || []);\n                }\n            } catch (error) {\n                console.error('Error fetching assigned trainers:', error);\n            }\n        }\n    }[\"MemberChatPage.useCallback[fetchAssignedTrainers]\"], [\n        user?.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberChatPage.useEffect\": ()=>{\n            fetchAssignedTrainers();\n        }\n    }[\"MemberChatPage.useEffect\"], [\n        fetchAssignedTrainers\n    ]);\n    // WebSocket event listeners\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberChatPage.useEffect\": ()=>{\n            if (!socket || !isConnected) return;\n            // Listen for real-time message updates\n            const handleNewMessage = {\n                \"MemberChatPage.useEffect.handleNewMessage\": (data)=>{\n                    if (data.trainerId === selectedTrainer && data.memberId === user?.id) {\n                        const newMessage = {\n                            id: data.messageId,\n                            content: data.content,\n                            sender: 'assistant',\n                            timestamp: data.timestamp,\n                            progress: data.progress,\n                            score: data.score\n                        };\n                        setMessages({\n                            \"MemberChatPage.useEffect.handleNewMessage\": (prev)=>[\n                                    ...prev,\n                                    newMessage\n                                ]\n                        }[\"MemberChatPage.useEffect.handleNewMessage\"]);\n                        setIsLoading(false);\n                    }\n                }\n            }[\"MemberChatPage.useEffect.handleNewMessage\"];\n            // Listen for typing indicators\n            const handleTypingStart = {\n                \"MemberChatPage.useEffect.handleTypingStart\": (data)=>{\n                    if (data.trainerId === selectedTrainer && data.memberId === user?.id) {\n                        setTypingIndicator(true);\n                    }\n                }\n            }[\"MemberChatPage.useEffect.handleTypingStart\"];\n            const handleTypingStop = {\n                \"MemberChatPage.useEffect.handleTypingStop\": (data)=>{\n                    if (data.trainerId === selectedTrainer && data.memberId === user?.id) {\n                        setTypingIndicator(false);\n                    }\n                }\n            }[\"MemberChatPage.useEffect.handleTypingStop\"];\n            // Listen for progress updates\n            const handleProgressUpdate = {\n                \"MemberChatPage.useEffect.handleProgressUpdate\": (data)=>{\n                    if (data.trainerId === selectedTrainer && data.memberId === user?.id) {\n                        // You could show a toast notification here\n                        console.log('Progress updated:', data);\n                    }\n                }\n            }[\"MemberChatPage.useEffect.handleProgressUpdate\"];\n            on('assistant_message', handleNewMessage);\n            on('assistant_typing_start', handleTypingStart);\n            on('assistant_typing_stop', handleTypingStop);\n            on('progress_updated', handleProgressUpdate);\n            return ({\n                \"MemberChatPage.useEffect\": ()=>{\n                    off('assistant_message', handleNewMessage);\n                    off('assistant_typing_start', handleTypingStart);\n                    off('assistant_typing_stop', handleTypingStop);\n                    off('progress_updated', handleProgressUpdate);\n                }\n            })[\"MemberChatPage.useEffect\"];\n        }\n    }[\"MemberChatPage.useEffect\"], [\n        socket,\n        isConnected,\n        selectedTrainer,\n        user?.id,\n        on,\n        off\n    ]);\n    const fetchTrainer = async (id)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.trainers.getById(id);\n            setTrainer(response.data.data);\n        } catch (error) {\n            console.error('Error fetching trainer:', error);\n        }\n    };\n    const initializeChat = async (trainerId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.chat.initialize(trainerId);\n            if (response.data.success) {\n                setSessionId(response.data.data.sessionId);\n                // Load conversation history if available\n                if (response.data.data.conversationHistory && response.data.data.conversationHistory.length > 0) {\n                    const formattedMessages = response.data.data.conversationHistory.map((msg)=>({\n                            id: msg.id,\n                            content: msg.content,\n                            sender: msg.role === 'user' ? 'user' : 'assistant',\n                            timestamp: msg.timestamp,\n                            progress: msg.progress,\n                            score: msg.score\n                        }));\n                    setMessages(formattedMessages);\n                } else {\n                    // Show welcome message if no history\n                    setMessages([\n                        {\n                            id: 1,\n                            content: 'Hello! I\\'m your AI trainer. How can I help you today?',\n                            sender: 'assistant',\n                            timestamp: new Date().toISOString()\n                        }\n                    ]);\n                }\n            }\n        } catch (error) {\n            console.error('Error initializing chat:', error);\n            // Fallback to welcome message\n            setMessages([\n                {\n                    id: 1,\n                    content: 'Hello! I\\'m your AI trainer. How can I help you today?',\n                    sender: 'assistant',\n                    timestamp: new Date().toISOString()\n                }\n            ]);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || !selectedTrainer || !sessionId) return;\n        const userMessage = {\n            id: Date.now(),\n            content: inputMessage,\n            sender: 'user',\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const messageToSend = inputMessage;\n        setInputMessage('');\n        setIsLoading(true);\n        // Add loading message\n        const loadingMessage = {\n            id: Date.now() + 1,\n            content: 'Thinking...',\n            sender: 'assistant',\n            timestamp: new Date().toISOString(),\n            isLoading: true\n        };\n        setMessages((prev)=>[\n                ...prev,\n                loadingMessage\n            ]);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.api.chat.sendMessage(selectedTrainer, {\n                message: messageToSend,\n                sessionId: sessionId\n            });\n            if (response.data.success) {\n                const assistantResponse = response.data.data.response;\n                const assistantMessage = {\n                    id: Date.now() + 2,\n                    content: assistantResponse.content,\n                    sender: 'assistant',\n                    timestamp: new Date().toISOString(),\n                    progress: assistantResponse.progress,\n                    score: assistantResponse.score\n                };\n                setMessages((prev)=>prev.filter((msg)=>!msg.isLoading).concat(assistantMessage));\n            } else {\n                throw new Error(response.data.error?.message || 'Failed to send message');\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Show error message\n            const errorMessage = {\n                id: Date.now() + 2,\n                content: 'Sorry, I encountered an error. Please try again.',\n                sender: 'assistant',\n                timestamp: new Date().toISOString()\n            };\n            setMessages((prev)=>prev.filter((msg)=>!msg.isLoading).concat(errorMessage));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const formatTime = (timestamp)=>{\n        const date = new Date(timestamp);\n        return date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    if (!trainerId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-80 bg-white border-r border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Your Trainers\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: assignedTrainers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: assignedTrainers.map((trainer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: `/member/chat?trainer=${trainer.id}`,\n                                        className: \"flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: trainer.name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: trainer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 truncate\",\n                                                        children: trainer.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, trainer.id, true, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No trainers assigned\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"Select a trainer to start chatting\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Choose from your assigned trainers to begin a conversation\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            href: \"/member/chat\",\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        trainer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-medium\",\n                                        children: trainer.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: trainer.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: trainer.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-900'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        message.sender === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mt-1\",\n                                            children: message.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                message.sender === 'assistant' && (message.progress !== undefined || message.score !== undefined) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-2 bg-green-50 rounded text-xs\",\n                                                    children: [\n                                                        message.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-700\",\n                                                                    children: \"Progress:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 bg-green-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-green-600 h-2 rounded-full transition-all duration-300\",\n                                                                        style: {\n                                                                            width: `${message.progress}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-700 font-medium\",\n                                                                    children: [\n                                                                        message.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        message.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-700\",\n                                                                    children: \"Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-700 font-medium\",\n                                                                    children: [\n                                                                        message.score,\n                                                                        \"/100\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: `text-xs mt-1 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                                                    children: formatTime(message.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        message.sender === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)),\n                    typingIndicator && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-200 text-gray-900\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: '0.1s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: '0.2s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"AI is typing...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: inputMessage,\n                            onChange: (e)=>setInputMessage(e.target.value),\n                            onKeyDown: handleKeyDown,\n                            placeholder: \"Type your message...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: sendMessage,\n                            disabled: !inputMessage.trim() || isLoading,\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bot_Loader2_Send_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/chat/page.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL21lbWJlci9jaGF0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RDtBQUNQO0FBQ047QUFDWjtBQUVvQjtBQVE5QjtBQUNPO0FBWWQsU0FBU2M7SUFDdEIsTUFBTUMsZUFBZVosZ0VBQWVBO0lBQ3BDLE1BQU1hLFlBQVlELGFBQWFFLEdBQUcsQ0FBQztJQUNuQyxNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHZCx5REFBWUE7SUFDN0IsTUFBTSxFQUFFZSxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsRUFBRSxFQUFFQyxHQUFHLEVBQUUsR0FBR2hCLGlFQUFZQTtJQUVyRCxNQUFNLENBQUNpQixTQUFTQyxXQUFXLEdBQUd2QiwrQ0FBUUEsQ0FBaUI7SUFDdkQsTUFBTSxDQUFDd0Isa0JBQWtCQyxvQkFBb0IsR0FBR3pCLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEUsTUFBTSxDQUFDMEIsVUFBVUMsWUFBWSxHQUFHM0IsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUM0QixjQUFjQyxnQkFBZ0IsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzhCLFdBQVdDLGFBQWEsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dDLGlCQUFpQkMsbUJBQW1CLEdBQUdqQywrQ0FBUUEsQ0FBZ0I7SUFDdEUsTUFBTSxDQUFDa0MsV0FBV0MsYUFBYSxHQUFHbkMsK0NBQVFBLENBQWdCO0lBQzFELE1BQU0sQ0FBQ29DLGlCQUFpQkMsbUJBQW1CLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUd2REQsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSWdCLFdBQVc7Z0JBQ2JrQixtQkFBbUJLLFNBQVN2QjtnQkFDNUJ3QixhQUFhRCxTQUFTdkI7Z0JBQ3RCeUIsZUFBZUYsU0FBU3ZCO1lBQzFCO1FBQ0Y7bUNBQUc7UUFBQ0E7S0FBVTtJQUVkLE1BQU0wQix3QkFBd0J4QyxrREFBV0E7NkRBQUM7WUFDeEMsSUFBSTtnQkFDRixJQUFJZ0IsTUFBTXlCLElBQUk7b0JBQ1osTUFBTUMsV0FBVyxNQUFNdkMseUNBQUdBLENBQUN3QyxPQUFPLENBQUNDLG1CQUFtQjtvQkFDdEQscUVBQXFFO29CQUNyRXBCLG9CQUFvQmtCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSSxFQUFFQyxZQUFZLEVBQUU7Z0JBQ3hEO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtZQUNyRDtRQUNGOzREQUFHO1FBQUMvQixNQUFNeUI7S0FBRztJQUViM0MsZ0RBQVNBO29DQUFDO1lBQ1IwQztRQUNGO21DQUFHO1FBQUNBO0tBQXNCO0lBRTFCLDRCQUE0QjtJQUM1QjFDLGdEQUFTQTtvQ0FBQztZQUNSLElBQUksQ0FBQ21CLFVBQVUsQ0FBQ0MsYUFBYTtZQUU3Qix1Q0FBdUM7WUFDdkMsTUFBTStCOzZEQUFtQixDQUFDSjtvQkFDeEIsSUFBSUEsS0FBSy9CLFNBQVMsS0FBS2lCLG1CQUFtQmMsS0FBS0ssUUFBUSxLQUFLbEMsTUFBTXlCLElBQUk7d0JBQ3BFLE1BQU1VLGFBQXNCOzRCQUMxQlYsSUFBSUksS0FBS08sU0FBUzs0QkFDbEJDLFNBQVNSLEtBQUtRLE9BQU87NEJBQ3JCQyxRQUFROzRCQUNSQyxXQUFXVixLQUFLVSxTQUFTOzRCQUN6QkMsVUFBVVgsS0FBS1csUUFBUTs0QkFDdkJDLE9BQU9aLEtBQUtZLEtBQUs7d0JBQ25CO3dCQUNBL0I7eUVBQVlnQyxDQUFBQSxPQUFRO3VDQUFJQTtvQ0FBTVA7aUNBQVc7O3dCQUN6Q3JCLGFBQWE7b0JBQ2Y7Z0JBQ0Y7O1lBRUEsK0JBQStCO1lBQy9CLE1BQU02Qjs4REFBb0IsQ0FBQ2Q7b0JBQ3pCLElBQUlBLEtBQUsvQixTQUFTLEtBQUtpQixtQkFBbUJjLEtBQUtLLFFBQVEsS0FBS2xDLE1BQU15QixJQUFJO3dCQUNwRUwsbUJBQW1CO29CQUNyQjtnQkFDRjs7WUFFQSxNQUFNd0I7NkRBQW1CLENBQUNmO29CQUN4QixJQUFJQSxLQUFLL0IsU0FBUyxLQUFLaUIsbUJBQW1CYyxLQUFLSyxRQUFRLEtBQUtsQyxNQUFNeUIsSUFBSTt3QkFDcEVMLG1CQUFtQjtvQkFDckI7Z0JBQ0Y7O1lBRUEsOEJBQThCO1lBQzlCLE1BQU15QjtpRUFBdUIsQ0FBQ2hCO29CQUM1QixJQUFJQSxLQUFLL0IsU0FBUyxLQUFLaUIsbUJBQW1CYyxLQUFLSyxRQUFRLEtBQUtsQyxNQUFNeUIsSUFBSTt3QkFDcEUsMkNBQTJDO3dCQUMzQ08sUUFBUWMsR0FBRyxDQUFDLHFCQUFxQmpCO29CQUNuQztnQkFDRjs7WUFFQTFCLEdBQUcscUJBQXFCOEI7WUFDeEI5QixHQUFHLDBCQUEwQndDO1lBQzdCeEMsR0FBRyx5QkFBeUJ5QztZQUM1QnpDLEdBQUcsb0JBQW9CMEM7WUFFdkI7NENBQU87b0JBQ0x6QyxJQUFJLHFCQUFxQjZCO29CQUN6QjdCLElBQUksMEJBQTBCdUM7b0JBQzlCdkMsSUFBSSx5QkFBeUJ3QztvQkFDN0J4QyxJQUFJLG9CQUFvQnlDO2dCQUMxQjs7UUFDRjttQ0FBRztRQUFDNUM7UUFBUUM7UUFBYWE7UUFBaUJmLE1BQU15QjtRQUFJdEI7UUFBSUM7S0FBSTtJQUU1RCxNQUFNa0IsZUFBZSxPQUFPRztRQUMxQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNdkMseUNBQUdBLENBQUMyQyxRQUFRLENBQUNpQixPQUFPLENBQUN0QjtZQUM1Q25CLFdBQVdvQixTQUFTRyxJQUFJLENBQUNBLElBQUk7UUFDL0IsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1FBQzNDO0lBQ0Y7SUFFQSxNQUFNUixpQkFBaUIsT0FBT3pCO1FBQzVCLElBQUk7WUFDRixNQUFNNEIsV0FBVyxNQUFNdkMseUNBQUdBLENBQUM2RCxJQUFJLENBQUNDLFVBQVUsQ0FBQ25EO1lBQzNDLElBQUk0QixTQUFTRyxJQUFJLENBQUNxQixPQUFPLEVBQUU7Z0JBQ3pCaEMsYUFBYVEsU0FBU0csSUFBSSxDQUFDQSxJQUFJLENBQUNaLFNBQVM7Z0JBRXpDLHlDQUF5QztnQkFDekMsSUFBSVMsU0FBU0csSUFBSSxDQUFDQSxJQUFJLENBQUNzQixtQkFBbUIsSUFBSXpCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSSxDQUFDc0IsbUJBQW1CLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUMvRixNQUFNQyxvQkFBb0IzQixTQUFTRyxJQUFJLENBQUNBLElBQUksQ0FBQ3NCLG1CQUFtQixDQUFDRyxHQUFHLENBQUMsQ0FBQ0MsTUFBYzs0QkFDbEY5QixJQUFJOEIsSUFBSTlCLEVBQUU7NEJBQ1ZZLFNBQVNrQixJQUFJbEIsT0FBTzs0QkFDcEJDLFFBQVFpQixJQUFJQyxJQUFJLEtBQUssU0FBUyxTQUFTOzRCQUN2Q2pCLFdBQVdnQixJQUFJaEIsU0FBUzs0QkFDeEJDLFVBQVVlLElBQUlmLFFBQVE7NEJBQ3RCQyxPQUFPYyxJQUFJZCxLQUFLO3dCQUNsQjtvQkFDQS9CLFlBQVkyQztnQkFDZCxPQUFPO29CQUNMLHFDQUFxQztvQkFDckMzQyxZQUFZO3dCQUNWOzRCQUNFZSxJQUFJOzRCQUNKWSxTQUFTOzRCQUNUQyxRQUFROzRCQUNSQyxXQUFXLElBQUlrQixPQUFPQyxXQUFXO3dCQUNuQztxQkFDRDtnQkFDSDtZQUNGO1FBQ0YsRUFBRSxPQUFPM0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyw4QkFBOEI7WUFDOUJyQixZQUFZO2dCQUNWO29CQUNFZSxJQUFJO29CQUNKWSxTQUFTO29CQUNUQyxRQUFRO29CQUNSQyxXQUFXLElBQUlrQixPQUFPQyxXQUFXO2dCQUNuQzthQUNEO1FBQ0g7SUFDRjtJQUVBLE1BQU1DLGNBQWM7UUFDbEIsSUFBSSxDQUFDaEQsYUFBYWlELElBQUksTUFBTSxDQUFDN0MsbUJBQW1CLENBQUNFLFdBQVc7UUFFNUQsTUFBTTRDLGNBQXVCO1lBQzNCcEMsSUFBSWdDLEtBQUtLLEdBQUc7WUFDWnpCLFNBQVMxQjtZQUNUMkIsUUFBUTtZQUNSQyxXQUFXLElBQUlrQixPQUFPQyxXQUFXO1FBQ25DO1FBRUFoRCxZQUFZZ0MsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1tQjthQUFZO1FBQzFDLE1BQU1FLGdCQUFnQnBEO1FBQ3RCQyxnQkFBZ0I7UUFDaEJFLGFBQWE7UUFFYixzQkFBc0I7UUFDdEIsTUFBTWtELGlCQUEwQjtZQUM5QnZDLElBQUlnQyxLQUFLSyxHQUFHLEtBQUs7WUFDakJ6QixTQUFTO1lBQ1RDLFFBQVE7WUFDUkMsV0FBVyxJQUFJa0IsT0FBT0MsV0FBVztZQUNqQzdDLFdBQVc7UUFDYjtRQUNBSCxZQUFZZ0MsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1zQjthQUFlO1FBRTdDLElBQUk7WUFDRixNQUFNdEMsV0FBVyxNQUFNdkMseUNBQUdBLENBQUM2RCxJQUFJLENBQUNXLFdBQVcsQ0FBQzVDLGlCQUFpQjtnQkFDM0RrRCxTQUFTRjtnQkFDVDlDLFdBQVdBO1lBQ2I7WUFFQSxJQUFJUyxTQUFTRyxJQUFJLENBQUNxQixPQUFPLEVBQUU7Z0JBQ3pCLE1BQU1nQixvQkFBb0J4QyxTQUFTRyxJQUFJLENBQUNBLElBQUksQ0FBQ0gsUUFBUTtnQkFDckQsTUFBTXlDLG1CQUE0QjtvQkFDaEMxQyxJQUFJZ0MsS0FBS0ssR0FBRyxLQUFLO29CQUNqQnpCLFNBQVM2QixrQkFBa0I3QixPQUFPO29CQUNsQ0MsUUFBUTtvQkFDUkMsV0FBVyxJQUFJa0IsT0FBT0MsV0FBVztvQkFDakNsQixVQUFVMEIsa0JBQWtCMUIsUUFBUTtvQkFDcENDLE9BQU95QixrQkFBa0J6QixLQUFLO2dCQUNoQztnQkFFQS9CLFlBQVlnQyxDQUFBQSxPQUFRQSxLQUFLMEIsTUFBTSxDQUFDYixDQUFBQSxNQUFPLENBQUNBLElBQUkxQyxTQUFTLEVBQUV3RCxNQUFNLENBQUNGO1lBQ2hFLE9BQU87Z0JBQ0wsTUFBTSxJQUFJRyxNQUFNNUMsU0FBU0csSUFBSSxDQUFDRSxLQUFLLEVBQUVrQyxXQUFXO1lBQ2xEO1FBQ0YsRUFBRSxPQUFPbEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUV4QyxxQkFBcUI7WUFDckIsTUFBTXdDLGVBQXdCO2dCQUM1QjlDLElBQUlnQyxLQUFLSyxHQUFHLEtBQUs7Z0JBQ2pCekIsU0FBUztnQkFDVEMsUUFBUTtnQkFDUkMsV0FBVyxJQUFJa0IsT0FBT0MsV0FBVztZQUNuQztZQUVBaEQsWUFBWWdDLENBQUFBLE9BQVFBLEtBQUswQixNQUFNLENBQUNiLENBQUFBLE1BQU8sQ0FBQ0EsSUFBSTFDLFNBQVMsRUFBRXdELE1BQU0sQ0FBQ0U7UUFDaEUsU0FBVTtZQUNSekQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNMEQsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQmpCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1rQixhQUFhLENBQUN0QztRQUNsQixNQUFNdUMsT0FBTyxJQUFJckIsS0FBS2xCO1FBQ3RCLE9BQU91QyxLQUFLQyxrQkFBa0IsQ0FBQyxFQUFFLEVBQUU7WUFBRUMsTUFBTTtZQUFXQyxRQUFRO1FBQVU7SUFDMUU7SUFFQSxJQUFJLENBQUNuRixXQUFXO1FBQ2QscUJBQ0UsOERBQUNvRjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0M7Z0NBQUdELFdBQVU7MENBQXNDOzs7Ozs7Ozs7OztzQ0FFdEQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaNUUsaUJBQWlCNkMsTUFBTSxHQUFHLGtCQUN6Qiw4REFBQzhCO2dDQUFJQyxXQUFVOzBDQUNaNUUsaUJBQWlCK0MsR0FBRyxDQUFDLENBQUNqRCx3QkFDckIsOERBQUNWLGtEQUFJQTt3Q0FFSDBGLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRWhGLFFBQVFvQixFQUFFLEVBQUU7d0NBQzFDMEQsV0FBVTs7MERBRVYsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRztvREFBS0gsV0FBVTs4REFDYjlFLFFBQVFrRixJQUFJLENBQUNDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXOzs7Ozs7Ozs7OzswREFHdkMsOERBQUNQO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ087d0RBQUdQLFdBQVU7a0VBQTZCOUUsUUFBUWtGLElBQUk7Ozs7OztrRUFDdkQsOERBQUNJO3dEQUFFUixXQUFVO2tFQUFrQzlFLFFBQVF1RixXQUFXOzs7Ozs7Ozs7Ozs7O3VDQVgvRHZGLFFBQVFvQixFQUFFOzs7Ozs7Ozs7cURBaUJyQiw4REFBQ3lEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3pGLGdIQUFJQTt3Q0FBQ3lGLFdBQVU7Ozs7OztrREFDaEIsOERBQUNRO3dDQUFFUixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3JDLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDN0YsZ0hBQUdBO2dDQUFDNkYsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDTztnQ0FBR1AsV0FBVTswQ0FBeUM7Ozs7OzswQ0FDdkQsOERBQUNRO2dDQUFFUixXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLdkM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUN4RixrREFBSUE7NEJBQ0gwRixNQUFLOzRCQUNMRixXQUFVO3NDQUVWLDRFQUFDMUYsZ0hBQVNBO2dDQUFDMEYsV0FBVTs7Ozs7Ozs7Ozs7d0JBRXRCOUUseUJBQ0M7OzhDQUNFLDhEQUFDNkU7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNHO3dDQUFLSCxXQUFVO2tEQUNiOUUsUUFBUWtGLElBQUksQ0FBQ0MsTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs7Ozs7OzhDQUd2Qyw4REFBQ1A7O3NEQUNDLDhEQUFDRTs0Q0FBR0QsV0FBVTtzREFBNkI5RSxRQUFRa0YsSUFBSTs7Ozs7O3NEQUN2RCw4REFBQ0k7NENBQUVSLFdBQVU7c0RBQXlCOUUsUUFBUXVGLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUW5FLDhEQUFDVjtnQkFBSUMsV0FBVTs7b0JBQ1oxRSxTQUFTNkMsR0FBRyxDQUFDLENBQUNXLHdCQUNiLDhEQUFDaUI7NEJBRUNDLFdBQVcsQ0FBQyxLQUFLLEVBQUVsQixRQUFRM0IsTUFBTSxLQUFLLFNBQVMsZ0JBQWdCLGlCQUFpQjtzQ0FFaEYsNEVBQUM0QztnQ0FDQ0MsV0FBVyxDQUFDLDBDQUEwQyxFQUNwRGxCLFFBQVEzQixNQUFNLEtBQUssU0FDZiwyQkFDQSw2QkFDSjswQ0FFRiw0RUFBQzRDO29DQUFJQyxXQUFVOzt3Q0FDWmxCLFFBQVEzQixNQUFNLEtBQUssNkJBQ2xCLDhEQUFDNEM7NENBQUlDLFdBQVU7c0RBQ1psQixRQUFRcEQsU0FBUyxpQkFDaEIsOERBQUNyQixpSEFBT0E7Z0RBQUMyRixXQUFVOzs7OztxRUFFbkIsOERBQUM3RixnSEFBR0E7Z0RBQUM2RixXQUFVOzs7Ozs7Ozs7OztzREFJckIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1E7b0RBQUVSLFdBQVU7OERBQVdsQixRQUFRNUIsT0FBTzs7Ozs7O2dEQUd0QzRCLFFBQVEzQixNQUFNLEtBQUssZUFBZ0IyQixDQUFBQSxRQUFRekIsUUFBUSxLQUFLcUQsYUFBYTVCLFFBQVF4QixLQUFLLEtBQUtvRCxTQUFRLG1CQUM5Riw4REFBQ1g7b0RBQUlDLFdBQVU7O3dEQUNabEIsUUFBUXpCLFFBQVEsS0FBS3FELDJCQUNwQiw4REFBQ1g7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRztvRUFBS0gsV0FBVTs4RUFBaUI7Ozs7Ozs4RUFDakMsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDRDt3RUFDQ0MsV0FBVTt3RUFDVlcsT0FBTzs0RUFBRUMsT0FBTyxHQUFHOUIsUUFBUXpCLFFBQVEsQ0FBQyxDQUFDLENBQUM7d0VBQUM7Ozs7Ozs7Ozs7OzhFQUczQyw4REFBQzhDO29FQUFLSCxXQUFVOzt3RUFBOEJsQixRQUFRekIsUUFBUTt3RUFBQzs7Ozs7Ozs7Ozs7Ozt3REFHbEV5QixRQUFReEIsS0FBSyxLQUFLb0QsMkJBQ2pCLDhEQUFDWDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNHO29FQUFLSCxXQUFVOzhFQUFpQjs7Ozs7OzhFQUNqQyw4REFBQ0c7b0VBQUtILFdBQVU7O3dFQUE4QmxCLFFBQVF4QixLQUFLO3dFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1wRSw4REFBQ2tEO29EQUFFUixXQUFXLENBQUMsYUFBYSxFQUMxQmxCLFFBQVEzQixNQUFNLEtBQUssU0FBUyxrQkFBa0IsaUJBQzlDOzhEQUVDdUMsV0FBV1osUUFBUTFCLFNBQVM7Ozs7Ozs7Ozs7Ozt3Q0FHaEMwQixRQUFRM0IsTUFBTSxLQUFLLHdCQUNsQiw4REFBQzRDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDNUYsaUhBQUlBO2dEQUFDNEYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQkF4RG5CbEIsUUFBUXhDLEVBQUU7Ozs7O29CQWlFbEJOLGlDQUNDLDhEQUFDK0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDN0YsZ0hBQUdBOzRDQUFDNkYsV0FBVTs7Ozs7Ozs7Ozs7a0RBRWpCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVO2dEQUFrRFcsT0FBTztvREFBRUUsZ0JBQWdCO2dEQUFPOzs7Ozs7MERBQ2pHLDhEQUFDZDtnREFBSUMsV0FBVTtnREFBa0RXLE9BQU87b0RBQUVFLGdCQUFnQjtnREFBTzs7Ozs7Ozs7Ozs7O2tEQUVuRyw4REFBQ1Y7d0NBQUtILFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFsRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2M7NEJBQ0NDLE1BQUs7NEJBQ0xDLE9BQU94Rjs0QkFDUHlGLFVBQVUsQ0FBQzNCLElBQU03RCxnQkFBZ0I2RCxFQUFFNEIsTUFBTSxDQUFDRixLQUFLOzRCQUMvQ0csV0FBVzlCOzRCQUNYK0IsYUFBWTs0QkFDWnBCLFdBQVU7NEJBQ1ZxQixVQUFVM0Y7Ozs7OztzQ0FFWiw4REFBQzRGOzRCQUNDQyxTQUFTL0M7NEJBQ1Q2QyxVQUFVLENBQUM3RixhQUFhaUQsSUFBSSxNQUFNL0M7NEJBQ2xDc0UsV0FBVTtzQ0FFVHRFLDBCQUNDLDhEQUFDckIsaUhBQU9BO2dDQUFDMkYsV0FBVTs7Ozs7cURBRW5CLDhEQUFDOUYsaUhBQUlBO2dDQUFDOEYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU85QiIsInNvdXJjZXMiOlsiL2hvbWUvcGlsZS9tZWV0X3RyYWluZXIvZnJvbnRlbmQvc3JjL2FwcC9tZW1iZXIvY2hhdC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmUvYXV0aCc7XG5pbXBvcnQgeyBhcGkgfSBmcm9tICdAL2xpYi9hcGknO1xuaW1wb3J0IHsgVHJhaW5lciB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgdXNlV2ViU29ja2V0IH0gZnJvbSAnQC9ob29rcy91c2VXZWJTb2NrZXQnO1xuaW1wb3J0IHtcbiAgU2VuZCxcbiAgQm90LFxuICBVc2VyLFxuICBMb2FkZXIyLFxuICBBcnJvd0xlZnQsXG4gIFN0YXJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5cbmludGVyZmFjZSBNZXNzYWdlIHtcbiAgaWQ6IG51bWJlcjtcbiAgY29udGVudDogc3RyaW5nO1xuICBzZW5kZXI6ICd1c2VyJyB8ICdhc3Npc3RhbnQnO1xuICB0aW1lc3RhbXA6IHN0cmluZztcbiAgaXNMb2FkaW5nPzogYm9vbGVhbjtcbiAgcHJvZ3Jlc3M/OiBudW1iZXI7XG4gIHNjb3JlPzogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNZW1iZXJDaGF0UGFnZSgpIHtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XG4gIGNvbnN0IHRyYWluZXJJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3RyYWluZXInKTtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoU3RvcmUoKTtcbiAgY29uc3QgeyBzb2NrZXQsIGlzQ29ubmVjdGVkLCBvbiwgb2ZmIH0gPSB1c2VXZWJTb2NrZXQoKTtcblxuICBjb25zdCBbdHJhaW5lciwgc2V0VHJhaW5lcl0gPSB1c2VTdGF0ZTxUcmFpbmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFthc3NpZ25lZFRyYWluZXJzLCBzZXRBc3NpZ25lZFRyYWluZXJzXSA9IHVzZVN0YXRlPFRyYWluZXJbXT4oW10pO1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xuICBjb25zdCBbaW5wdXRNZXNzYWdlLCBzZXRJbnB1dE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VsZWN0ZWRUcmFpbmVyLCBzZXRTZWxlY3RlZFRyYWluZXJdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZXNzaW9uSWQsIHNldFNlc3Npb25JZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3R5cGluZ0luZGljYXRvciwgc2V0VHlwaW5nSW5kaWNhdG9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHRyYWluZXJJZCkge1xuICAgICAgc2V0U2VsZWN0ZWRUcmFpbmVyKHBhcnNlSW50KHRyYWluZXJJZCkpO1xuICAgICAgZmV0Y2hUcmFpbmVyKHBhcnNlSW50KHRyYWluZXJJZCkpO1xuICAgICAgaW5pdGlhbGl6ZUNoYXQocGFyc2VJbnQodHJhaW5lcklkKSk7XG4gICAgfVxuICB9LCBbdHJhaW5lcklkXSk7XG5cbiAgY29uc3QgZmV0Y2hBc3NpZ25lZFRyYWluZXJzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAodXNlcj8uaWQpIHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkubWVtYmVycy5nZXRBc3NpZ25lZFRyYWluZXJzKCk7XG4gICAgICAgIC8vIEhhbmRsZSB0aGUgY29ycmVjdCByZXNwb25zZSBzdHJ1Y3R1cmU6IHJlc3BvbnNlLmRhdGEuZGF0YS50cmFpbmVyc1xuICAgICAgICBzZXRBc3NpZ25lZFRyYWluZXJzKHJlc3BvbnNlLmRhdGEuZGF0YT8udHJhaW5lcnMgfHwgW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhc3NpZ25lZCB0cmFpbmVyczonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbdXNlcj8uaWRdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoQXNzaWduZWRUcmFpbmVycygpO1xuICB9LCBbZmV0Y2hBc3NpZ25lZFRyYWluZXJzXSk7XG5cbiAgLy8gV2ViU29ja2V0IGV2ZW50IGxpc3RlbmVyc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghc29ja2V0IHx8ICFpc0Nvbm5lY3RlZCkgcmV0dXJuO1xuXG4gICAgLy8gTGlzdGVuIGZvciByZWFsLXRpbWUgbWVzc2FnZSB1cGRhdGVzXG4gICAgY29uc3QgaGFuZGxlTmV3TWVzc2FnZSA9IChkYXRhOiBhbnkpID0+IHtcbiAgICAgIGlmIChkYXRhLnRyYWluZXJJZCA9PT0gc2VsZWN0ZWRUcmFpbmVyICYmIGRhdGEubWVtYmVySWQgPT09IHVzZXI/LmlkKSB7XG4gICAgICAgIGNvbnN0IG5ld01lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICAgICAgaWQ6IGRhdGEubWVzc2FnZUlkLFxuICAgICAgICAgIGNvbnRlbnQ6IGRhdGEuY29udGVudCxcbiAgICAgICAgICBzZW5kZXI6ICdhc3Npc3RhbnQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogZGF0YS50aW1lc3RhbXAsXG4gICAgICAgICAgcHJvZ3Jlc3M6IGRhdGEucHJvZ3Jlc3MsXG4gICAgICAgICAgc2NvcmU6IGRhdGEuc2NvcmVcbiAgICAgICAgfTtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgbmV3TWVzc2FnZV0pO1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBMaXN0ZW4gZm9yIHR5cGluZyBpbmRpY2F0b3JzXG4gICAgY29uc3QgaGFuZGxlVHlwaW5nU3RhcnQgPSAoZGF0YTogYW55KSA9PiB7XG4gICAgICBpZiAoZGF0YS50cmFpbmVySWQgPT09IHNlbGVjdGVkVHJhaW5lciAmJiBkYXRhLm1lbWJlcklkID09PSB1c2VyPy5pZCkge1xuICAgICAgICBzZXRUeXBpbmdJbmRpY2F0b3IodHJ1ZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZVR5cGluZ1N0b3AgPSAoZGF0YTogYW55KSA9PiB7XG4gICAgICBpZiAoZGF0YS50cmFpbmVySWQgPT09IHNlbGVjdGVkVHJhaW5lciAmJiBkYXRhLm1lbWJlcklkID09PSB1c2VyPy5pZCkge1xuICAgICAgICBzZXRUeXBpbmdJbmRpY2F0b3IoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBMaXN0ZW4gZm9yIHByb2dyZXNzIHVwZGF0ZXNcbiAgICBjb25zdCBoYW5kbGVQcm9ncmVzc1VwZGF0ZSA9IChkYXRhOiBhbnkpID0+IHtcbiAgICAgIGlmIChkYXRhLnRyYWluZXJJZCA9PT0gc2VsZWN0ZWRUcmFpbmVyICYmIGRhdGEubWVtYmVySWQgPT09IHVzZXI/LmlkKSB7XG4gICAgICAgIC8vIFlvdSBjb3VsZCBzaG93IGEgdG9hc3Qgbm90aWZpY2F0aW9uIGhlcmVcbiAgICAgICAgY29uc29sZS5sb2coJ1Byb2dyZXNzIHVwZGF0ZWQ6JywgZGF0YSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIG9uKCdhc3Npc3RhbnRfbWVzc2FnZScsIGhhbmRsZU5ld01lc3NhZ2UpO1xuICAgIG9uKCdhc3Npc3RhbnRfdHlwaW5nX3N0YXJ0JywgaGFuZGxlVHlwaW5nU3RhcnQpO1xuICAgIG9uKCdhc3Npc3RhbnRfdHlwaW5nX3N0b3AnLCBoYW5kbGVUeXBpbmdTdG9wKTtcbiAgICBvbigncHJvZ3Jlc3NfdXBkYXRlZCcsIGhhbmRsZVByb2dyZXNzVXBkYXRlKTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBvZmYoJ2Fzc2lzdGFudF9tZXNzYWdlJywgaGFuZGxlTmV3TWVzc2FnZSk7XG4gICAgICBvZmYoJ2Fzc2lzdGFudF90eXBpbmdfc3RhcnQnLCBoYW5kbGVUeXBpbmdTdGFydCk7XG4gICAgICBvZmYoJ2Fzc2lzdGFudF90eXBpbmdfc3RvcCcsIGhhbmRsZVR5cGluZ1N0b3ApO1xuICAgICAgb2ZmKCdwcm9ncmVzc191cGRhdGVkJywgaGFuZGxlUHJvZ3Jlc3NVcGRhdGUpO1xuICAgIH07XG4gIH0sIFtzb2NrZXQsIGlzQ29ubmVjdGVkLCBzZWxlY3RlZFRyYWluZXIsIHVzZXI/LmlkLCBvbiwgb2ZmXSk7XG5cbiAgY29uc3QgZmV0Y2hUcmFpbmVyID0gYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkudHJhaW5lcnMuZ2V0QnlJZChpZCk7XG4gICAgICBzZXRUcmFpbmVyKHJlc3BvbnNlLmRhdGEuZGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHRyYWluZXI6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpbml0aWFsaXplQ2hhdCA9IGFzeW5jICh0cmFpbmVySWQ6IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5jaGF0LmluaXRpYWxpemUodHJhaW5lcklkKTtcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0U2Vzc2lvbklkKHJlc3BvbnNlLmRhdGEuZGF0YS5zZXNzaW9uSWQpO1xuXG4gICAgICAgIC8vIExvYWQgY29udmVyc2F0aW9uIGhpc3RvcnkgaWYgYXZhaWxhYmxlXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmRhdGEuY29udmVyc2F0aW9uSGlzdG9yeSAmJiByZXNwb25zZS5kYXRhLmRhdGEuY29udmVyc2F0aW9uSGlzdG9yeS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3QgZm9ybWF0dGVkTWVzc2FnZXMgPSByZXNwb25zZS5kYXRhLmRhdGEuY29udmVyc2F0aW9uSGlzdG9yeS5tYXAoKG1zZzogYW55KSA9PiAoe1xuICAgICAgICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IG1zZy5jb250ZW50LFxuICAgICAgICAgICAgc2VuZGVyOiBtc2cucm9sZSA9PT0gJ3VzZXInID8gJ3VzZXInIDogJ2Fzc2lzdGFudCcsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG1zZy50aW1lc3RhbXAsXG4gICAgICAgICAgICBwcm9ncmVzczogbXNnLnByb2dyZXNzLFxuICAgICAgICAgICAgc2NvcmU6IG1zZy5zY29yZVxuICAgICAgICAgIH0pKTtcbiAgICAgICAgICBzZXRNZXNzYWdlcyhmb3JtYXR0ZWRNZXNzYWdlcyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gU2hvdyB3ZWxjb21lIG1lc3NhZ2UgaWYgbm8gaGlzdG9yeVxuICAgICAgICAgIHNldE1lc3NhZ2VzKFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IDEsXG4gICAgICAgICAgICAgIGNvbnRlbnQ6ICdIZWxsbyEgSVxcJ20geW91ciBBSSB0cmFpbmVyLiBIb3cgY2FuIEkgaGVscCB5b3UgdG9kYXk/JyxcbiAgICAgICAgICAgICAgc2VuZGVyOiAnYXNzaXN0YW50JyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgY2hhdDonLCBlcnJvcik7XG4gICAgICAvLyBGYWxsYmFjayB0byB3ZWxjb21lIG1lc3NhZ2VcbiAgICAgIHNldE1lc3NhZ2VzKFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAxLFxuICAgICAgICAgIGNvbnRlbnQ6ICdIZWxsbyEgSVxcJ20geW91ciBBSSB0cmFpbmVyLiBIb3cgY2FuIEkgaGVscCB5b3UgdG9kYXk/JyxcbiAgICAgICAgICBzZW5kZXI6ICdhc3Npc3RhbnQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH1cbiAgICAgIF0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzZW5kTWVzc2FnZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWlucHV0TWVzc2FnZS50cmltKCkgfHwgIXNlbGVjdGVkVHJhaW5lciB8fCAhc2Vzc2lvbklkKSByZXR1cm47XG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLFxuICAgICAgY29udGVudDogaW5wdXRNZXNzYWdlLFxuICAgICAgc2VuZGVyOiAndXNlcicsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH07XG5cbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xuICAgIGNvbnN0IG1lc3NhZ2VUb1NlbmQgPSBpbnB1dE1lc3NhZ2U7XG4gICAgc2V0SW5wdXRNZXNzYWdlKCcnKTtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICAvLyBBZGQgbG9hZGluZyBtZXNzYWdlXG4gICAgY29uc3QgbG9hZGluZ01lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKSArIDEsXG4gICAgICBjb250ZW50OiAnVGhpbmtpbmcuLi4nLFxuICAgICAgc2VuZGVyOiAnYXNzaXN0YW50JyxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgaXNMb2FkaW5nOiB0cnVlXG4gICAgfTtcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBsb2FkaW5nTWVzc2FnZV0pO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmNoYXQuc2VuZE1lc3NhZ2Uoc2VsZWN0ZWRUcmFpbmVyLCB7XG4gICAgICAgIG1lc3NhZ2U6IG1lc3NhZ2VUb1NlbmQsXG4gICAgICAgIHNlc3Npb25JZDogc2Vzc2lvbklkXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBjb25zdCBhc3Npc3RhbnRSZXNwb25zZSA9IHJlc3BvbnNlLmRhdGEuZGF0YS5yZXNwb25zZTtcbiAgICAgICAgY29uc3QgYXNzaXN0YW50TWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICBpZDogRGF0ZS5ub3coKSArIDIsXG4gICAgICAgICAgY29udGVudDogYXNzaXN0YW50UmVzcG9uc2UuY29udGVudCxcbiAgICAgICAgICBzZW5kZXI6ICdhc3Npc3RhbnQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHByb2dyZXNzOiBhc3Npc3RhbnRSZXNwb25zZS5wcm9ncmVzcyxcbiAgICAgICAgICBzY29yZTogYXNzaXN0YW50UmVzcG9uc2Uuc2NvcmVcbiAgICAgICAgfTtcblxuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYuZmlsdGVyKG1zZyA9PiAhbXNnLmlzTG9hZGluZykuY29uY2F0KGFzc2lzdGFudE1lc3NhZ2UpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5kYXRhLmVycm9yPy5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gc2VuZCBtZXNzYWdlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgbWVzc2FnZTonLCBlcnJvcik7XG5cbiAgICAgIC8vIFNob3cgZXJyb3IgbWVzc2FnZVxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlOiBNZXNzYWdlID0ge1xuICAgICAgICBpZDogRGF0ZS5ub3coKSArIDIsXG4gICAgICAgIGNvbnRlbnQ6ICdTb3JyeSwgSSBlbmNvdW50ZXJlZCBhbiBlcnJvci4gUGxlYXNlIHRyeSBhZ2Fpbi4nLFxuICAgICAgICBzZW5kZXI6ICdhc3Npc3RhbnQnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfTtcblxuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2LmZpbHRlcihtc2cgPT4gIW1zZy5pc0xvYWRpbmcpLmNvbmNhdChlcnJvck1lc3NhZ2UpKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBzZW5kTWVzc2FnZSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRUaW1lID0gKHRpbWVzdGFtcDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7XG4gICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKFtdLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSk7XG4gIH07XG5cbiAgaWYgKCF0cmFpbmVySWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleFwiPlxuICAgICAgICB7LyogVHJhaW5lciBMaXN0ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctODAgYmctd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5Zb3VyIFRyYWluZXJzPC9oMj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAge2Fzc2lnbmVkVHJhaW5lcnMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICB7YXNzaWduZWRUcmFpbmVycy5tYXAoKHRyYWluZXIpID0+IChcbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGtleT17dHJhaW5lci5pZH1cbiAgICAgICAgICAgICAgICAgICAgaHJlZj17YC9tZW1iZXIvY2hhdD90cmFpbmVyPSR7dHJhaW5lci5pZH1gfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBwLTMgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYmx1ZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5lci5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnt0cmFpbmVyLm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgdHJ1bmNhdGVcIj57dHJhaW5lci5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktMzAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPk5vIHRyYWluZXJzIGFzc2lnbmVkPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBFbXB0eSBTdGF0ZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxCb3QgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlNlbGVjdCBhIHRyYWluZXIgdG8gc3RhcnQgY2hhdHRpbmc8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPkNob29zZSBmcm9tIHlvdXIgYXNzaWduZWQgdHJhaW5lcnMgdG8gYmVnaW4gYSBjb252ZXJzYXRpb248L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTQgcHktM1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICBocmVmPVwiL21lbWJlci9jaGF0XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAge3RyYWluZXIgJiYgKFxuICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAge3RyYWluZXIubmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3RyYWluZXIubmFtZX08L2gyPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPnt0cmFpbmVyLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8Lz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWVzc2FnZXMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00IHNwYWNlLXktNFwiPlxuICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCAke21lc3NhZ2Uuc2VuZGVyID09PSAndXNlcicgPyAnanVzdGlmeS1lbmQnIDogJ2p1c3RpZnktc3RhcnQnfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BtYXgtdy14cyBsZzptYXgtdy1tZCBweC00IHB5LTIgcm91bmRlZC1sZyAke1xuICAgICAgICAgICAgICAgIG1lc3NhZ2Uuc2VuZGVyID09PSAndXNlcidcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTIwMCB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIHttZXNzYWdlLnNlbmRlciA9PT0gJ2Fzc2lzdGFudCcgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAge21lc3NhZ2UuaXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8Qm90IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiPnttZXNzYWdlLmNvbnRlbnR9PC9wPlxuXG4gICAgICAgICAgICAgICAgICB7LyogUHJvZ3Jlc3MgYW5kIFNjb3JlIERpc3BsYXkgKi99XG4gICAgICAgICAgICAgICAgICB7bWVzc2FnZS5zZW5kZXIgPT09ICdhc3Npc3RhbnQnICYmIChtZXNzYWdlLnByb2dyZXNzICE9PSB1bmRlZmluZWQgfHwgbWVzc2FnZS5zY29yZSAhPT0gdW5kZWZpbmVkKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBwLTIgYmctZ3JlZW4tNTAgcm91bmRlZCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2UucHJvZ3Jlc3MgIT09IHVuZGVmaW5lZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTcwMFwiPlByb2dyZXNzOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JlZW4tMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke21lc3NhZ2UucHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi03MDAgZm9udC1tZWRpdW1cIj57bWVzc2FnZS5wcm9ncmVzc30lPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICB7bWVzc2FnZS5zY29yZSAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi03MDBcIj5TY29yZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNzAwIGZvbnQtbWVkaXVtXCI+e21lc3NhZ2Uuc2NvcmV9LzEwMDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC14cyBtdC0xICR7XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2Uuc2VuZGVyID09PSAndXNlcicgPyAndGV4dC1ibHVlLTEwMCcgOiAndGV4dC1ncmF5LTUwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZShtZXNzYWdlLnRpbWVzdGFtcCl9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAge21lc3NhZ2Uuc2VuZGVyID09PSAndXNlcicgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cblxuICAgICAgICB7LyogVHlwaW5nIEluZGljYXRvciAqL31cbiAgICAgICAge3R5cGluZ0luZGljYXRvciAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktc3RhcnRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXcteHMgbGc6bWF4LXctbWQgcHgtNCBweS0yIHJvdW5kZWQtbGcgYmctZ3JheS0yMDAgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgPEJvdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JheS01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JheS01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjFzJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmF5LTUwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMnMnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkFJIGlzIHR5cGluZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSW5wdXQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2lucHV0TWVzc2FnZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVHlwZSB5b3VyIG1lc3NhZ2UuLi5cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17c2VuZE1lc3NhZ2V9XG4gICAgICAgICAgICBkaXNhYmxlZD17IWlucHV0TWVzc2FnZS50cmltKCkgfHwgaXNMb2FkaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC01IHctNSBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlU2VhcmNoUGFyYW1zIiwidXNlQXV0aFN0b3JlIiwiYXBpIiwidXNlV2ViU29ja2V0IiwiU2VuZCIsIkJvdCIsIlVzZXIiLCJMb2FkZXIyIiwiQXJyb3dMZWZ0IiwiU3RhciIsIkxpbmsiLCJNZW1iZXJDaGF0UGFnZSIsInNlYXJjaFBhcmFtcyIsInRyYWluZXJJZCIsImdldCIsInVzZXIiLCJzb2NrZXQiLCJpc0Nvbm5lY3RlZCIsIm9uIiwib2ZmIiwidHJhaW5lciIsInNldFRyYWluZXIiLCJhc3NpZ25lZFRyYWluZXJzIiwic2V0QXNzaWduZWRUcmFpbmVycyIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpbnB1dE1lc3NhZ2UiLCJzZXRJbnB1dE1lc3NhZ2UiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZWxlY3RlZFRyYWluZXIiLCJzZXRTZWxlY3RlZFRyYWluZXIiLCJzZXNzaW9uSWQiLCJzZXRTZXNzaW9uSWQiLCJ0eXBpbmdJbmRpY2F0b3IiLCJzZXRUeXBpbmdJbmRpY2F0b3IiLCJwYXJzZUludCIsImZldGNoVHJhaW5lciIsImluaXRpYWxpemVDaGF0IiwiZmV0Y2hBc3NpZ25lZFRyYWluZXJzIiwiaWQiLCJyZXNwb25zZSIsIm1lbWJlcnMiLCJnZXRBc3NpZ25lZFRyYWluZXJzIiwiZGF0YSIsInRyYWluZXJzIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlTmV3TWVzc2FnZSIsIm1lbWJlcklkIiwibmV3TWVzc2FnZSIsIm1lc3NhZ2VJZCIsImNvbnRlbnQiLCJzZW5kZXIiLCJ0aW1lc3RhbXAiLCJwcm9ncmVzcyIsInNjb3JlIiwicHJldiIsImhhbmRsZVR5cGluZ1N0YXJ0IiwiaGFuZGxlVHlwaW5nU3RvcCIsImhhbmRsZVByb2dyZXNzVXBkYXRlIiwibG9nIiwiZ2V0QnlJZCIsImNoYXQiLCJpbml0aWFsaXplIiwic3VjY2VzcyIsImNvbnZlcnNhdGlvbkhpc3RvcnkiLCJsZW5ndGgiLCJmb3JtYXR0ZWRNZXNzYWdlcyIsIm1hcCIsIm1zZyIsInJvbGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzZW5kTWVzc2FnZSIsInRyaW0iLCJ1c2VyTWVzc2FnZSIsIm5vdyIsIm1lc3NhZ2VUb1NlbmQiLCJsb2FkaW5nTWVzc2FnZSIsIm1lc3NhZ2UiLCJhc3Npc3RhbnRSZXNwb25zZSIsImFzc2lzdGFudE1lc3NhZ2UiLCJmaWx0ZXIiLCJjb25jYXQiLCJFcnJvciIsImVycm9yTWVzc2FnZSIsImhhbmRsZUtleURvd24iLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImZvcm1hdFRpbWUiLCJkYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiaG91ciIsIm1pbnV0ZSIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiaHJlZiIsInNwYW4iLCJuYW1lIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJoMyIsInAiLCJkZXNjcmlwdGlvbiIsInVuZGVmaW5lZCIsInN0eWxlIiwid2lkdGgiLCJhbmltYXRpb25EZWxheSIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleURvd24iLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/member/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/member/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/member/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemberLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/member/dashboard',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Chat',\n        href: '/member/chat',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Progress',\n        href: '/member/progress',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'History',\n        href: '/member/history',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/member/profile',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction MemberLayout({ children }) {\n    const { user, isAuthenticated, logout, isLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasCheckedAuth, setHasCheckedAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            setIsHydrated(true);\n        }\n    }[\"MemberLayout.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            // Don't redirect during SSR or hydration\n            if (!isHydrated) {\n                console.log('Member layout: Waiting for hydration');\n                return;\n            }\n            // Don't redirect while loading\n            if (isLoading) {\n                console.log('Member layout: Auth is loading');\n                return;\n            }\n            // Mark that we've checked auth at least once\n            setHasCheckedAuth(true);\n            // Check for auth token in localStorage as fallback\n            const token = localStorage.getItem('auth_token');\n            const storedAuthState = localStorage.getItem('auth-store');\n            const userRole = user?.role || 'member';\n            console.log('Member layout auth check:', {\n                isAuthenticated,\n                user: user ? {\n                    id: user.id,\n                    role: userRole,\n                    name: user.name\n                } : null,\n                hasToken: !!token,\n                hasStoredAuth: !!storedAuthState,\n                pathname,\n                isLoading,\n                isHydrated,\n                hasCheckedAuth\n            });\n            // Debug localStorage content\n            if (token) {\n                console.log('Member layout: Token found:', token.substring(0, 50) + '...');\n            }\n            if (storedAuthState) {\n                try {\n                    const parsed = JSON.parse(storedAuthState);\n                    console.log('Member layout: Stored auth state:', {\n                        hasUser: !!parsed.state?.user,\n                        isAuthenticated: parsed.state?.isAuthenticated,\n                        userRole: parsed.state?.user?.role\n                    });\n                } catch (e) {\n                    console.log('Member layout: Failed to parse stored auth state');\n                }\n            }\n            // If we have a token but no auth state, give it some time to load\n            if (token && !isAuthenticated && !user) {\n                console.log('Member layout: Have token but no auth state, waiting...');\n                return;\n            }\n            if (!isAuthenticated && !token) {\n                console.log('Member layout: Not authenticated and no token, redirecting to login');\n                window.location.href = '/auth/login';\n                return;\n            }\n            // For members, role is always 'member' or might not be present in user object\n            // Members don't have role field in database, it's implied from JWT token\n            if (isAuthenticated && user && userRole !== 'member') {\n                console.log('Member layout: User role is not member:', userRole);\n                window.location.href = '/auth/login';\n                return;\n            }\n            console.log('Member layout: Auth check passed - user is authenticated member');\n        }\n    }[\"MemberLayout.useEffect\"], [\n        isAuthenticated,\n        user,\n        router,\n        isLoading,\n        isHydrated,\n        pathname\n    ]);\n    const handleLogout = ()=>{\n        logout();\n        window.location.href = '/auth/login';\n    };\n    // Show loading during SSR/hydration or if we haven't checked auth yet\n    if (!isHydrated || isLoading || !hasCheckedAuth && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    // Check for token as fallback if auth state not ready\n    const token = localStorage.getItem('auth_token');\n    if (!isAuthenticated && !token) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-col w-64 bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-16 px-4 bg-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Member Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"text-white hover:text-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 px-4 py-4 space-y-2\",\n                                children: navigation.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: item.href,\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col bg-white shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16 px-4 bg-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"Member Portal\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-4 space-y-2\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden text-gray-500 hover:text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: user?.name?.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: user?.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/member/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Environment configuration\nconst config = {\n    // API Configuration\n    api: {\n        baseURL: \"http://localhost:3002/api\" || 0,\n        timeout: 10000\n    },\n    // WebSocket Configuration\n    websocket: {\n        url: \"http://localhost:3002\" || 0,\n        reconnectAttempts: 5,\n        reconnectDelay: 1000\n    },\n    // App Configuration\n    app: {\n        name: process.env.NEXT_PUBLIC_APP_NAME || 'AI Trainer Admin',\n        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n        environment: \"development\" || 0\n    },\n    // Authentication\n    auth: {\n        tokenKey: 'auth_token',\n        tokenExpiry: 24 * 60 * 60 * 1000\n    },\n    // UI Configuration\n    ui: {\n        defaultPageSize: 10,\n        maxPageSize: 100,\n        autoRefreshInterval: 5000\n    },\n    // Feature Flags\n    features: {\n        enableWebSocket: true,\n        enableNotifications: true,\n        enableAnalytics: true\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useWebSocket.ts":
/*!***********************************!*\
  !*** ./src/hooks/useWebSocket.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config */ \"(ssr)/./src/config/index.ts\");\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \n\n\n\nconst useWebSocket = ()=>{\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { user, token } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            if (!user || !token) return;\n            // Initialize socket connection\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(_config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.url, {\n                auth: {\n                    token: token\n                },\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                reconnectionAttempts: _config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.reconnectAttempts,\n                reconnectionDelay: _config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.reconnectDelay\n            });\n            socketRef.current = socket;\n            // Connection event handlers\n            socket.on('connect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    console.log('WebSocket connected');\n                    setIsConnected(true);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('disconnect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    console.log('WebSocket disconnected');\n                    setIsConnected(false);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('connect_error', {\n                \"useWebSocket.useEffect\": (error)=>{\n                    console.error('WebSocket connection error:', error);\n                    setIsConnected(false);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Cleanup on unmount\n            return ({\n                \"useWebSocket.useEffect\": ()=>{\n                    socket.disconnect();\n                    socketRef.current = null;\n                    setIsConnected(false);\n                }\n            })[\"useWebSocket.useEffect\"];\n        }\n    }[\"useWebSocket.useEffect\"], [\n        user,\n        token\n    ]);\n    const emit = (event, data)=>{\n        if (socketRef.current && isConnected) {\n            socketRef.current.emit(event, data);\n        }\n    };\n    const on = (event, callback)=>{\n        if (socketRef.current) {\n            socketRef.current.on(event, callback);\n        }\n    };\n    const off = (event, callback)=>{\n        if (socketRef.current) {\n            if (callback) {\n                socketRef.current.off(event, callback);\n            } else {\n                socketRef.current.off(event);\n            }\n        }\n    };\n    return {\n        socket: socketRef.current,\n        isConnected,\n        emit,\n        on,\n        off\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useWebSocket.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(ssr)/./src/config/index.ts\");\n\n\n// Create axios instance\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: _config__WEBPACK_IMPORTED_MODULE_0__[\"default\"].api.baseURL,\n    timeout: _config__WEBPACK_IMPORTED_MODULE_0__[\"default\"].api.timeout,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor untuk menambahkan token\napiClient.interceptors.request.use((requestConfig)=>{\n    const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auth.tokenKey);\n    if (token) {\n        requestConfig.headers.Authorization = `Bearer ${token}`;\n    }\n    return requestConfig;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor untuk handle errors\napiClient.interceptors.response.use((response)=>response, (error)=>{\n    // Only redirect to login on 401 if we're not already on the login page\n    if (error.response?.status === 401) {\n        // Check if we're already on login page to prevent redirect loops\n        if (false) {}\n    }\n    // Add better error logging for debugging\n    if (true) {\n        console.error('API Error:', {\n            status: error.response?.status,\n            message: error.response?.data?.error?.message || error.message,\n            url: error.config?.url,\n            method: error.config?.method\n        });\n    }\n    return Promise.reject(error);\n});\n// API functions\nconst api = {\n    // Auth\n    auth: {\n        login: (credentials)=>apiClient.post('/auth/login', credentials),\n        logout: ()=>apiClient.post('/auth/logout'),\n        getProfile: ()=>apiClient.get('/auth/profile'),\n        registerAdmin: (data)=>apiClient.post('/auth/register/admin', data),\n        registerMember: (data)=>apiClient.post('/auth/register/member', data)\n    },\n    // Admins\n    admins: {\n        getAll: (params)=>apiClient.get('/admins', {\n                params\n            }),\n        getById: (id)=>apiClient.get(`/admins/${id}`),\n        create: (data)=>apiClient.post('/auth/register/admin', data),\n        update: (id, data)=>apiClient.put(`/admins/${id}`, data),\n        delete: (id)=>apiClient.delete(`/admins/${id}`)\n    },\n    // Trainers\n    trainers: {\n        getAll: (params)=>apiClient.get('/trainers', {\n                params\n            }),\n        getById: (id)=>apiClient.get(`/trainers/${id}`),\n        create: (data)=>apiClient.post('/trainers', data),\n        update: (id, data)=>apiClient.put(`/trainers/${id}`, data),\n        delete: (id)=>apiClient.delete(`/trainers/${id}`),\n        assign: (id, memberIds)=>apiClient.post(`/trainers/${id}/assign`, {\n                memberIds\n            }),\n        unassign: (id, memberId)=>apiClient.delete(`/trainers/${id}/assign/${memberId}`),\n        getStatistics: (id)=>apiClient.get(`/trainers/${id}/statistics`)\n    },\n    // Trainer Submodules\n    submodules: {\n        getByTrainer: (trainerId)=>apiClient.get(`/trainers/${trainerId}/submodules`),\n        create: (trainerId, data)=>apiClient.post(`/trainers/${trainerId}/submodules`, data),\n        getById: (id)=>apiClient.get(`/submodules/${id}`),\n        update: (id, data)=>apiClient.put(`/submodules/${id}`, data),\n        delete: (id)=>apiClient.delete(`/submodules/${id}`),\n        reorder: (trainerId, submoduleIds)=>apiClient.put(`/trainers/${trainerId}/submodules/reorder`, {\n                submoduleIds\n            }),\n        validatePrompt: (systemPrompt)=>apiClient.post('/submodules/validate-prompt', {\n                systemPrompt\n            })\n    },\n    // Members\n    members: {\n        getAll: (params)=>apiClient.get('/members', {\n                params\n            }),\n        getById: (id)=>apiClient.get(`/members/${id}`),\n        create: (data)=>apiClient.post('/auth/register/member', data),\n        update: (id, data)=>apiClient.put(`/members/${id}`, data),\n        delete: (id)=>apiClient.delete(`/members/${id}`),\n        getAssignedTrainers: ()=>apiClient.get('/trainers/member')\n    },\n    // Monitoring\n    monitoring: {\n        getDashboard: ()=>apiClient.get('/monitoring/dashboard'),\n        getSuperAdminDashboard: ()=>apiClient.get('/monitoring/super-admin/dashboard'),\n        getConversations: (params)=>apiClient.get('/monitoring/conversations', {\n                params\n            }),\n        getStatistics: ()=>apiClient.get('/monitoring/statistics')\n    },\n    // Progress\n    progress: {\n        getByMember: (memberId)=>apiClient.get(`/progress/member/${memberId}`),\n        getByTrainer: (trainerId)=>apiClient.get(`/progress/trainer/${trainerId}`),\n        getTrends: (memberId, timeRange)=>apiClient.get(`/progress/member/${memberId}/trends`, {\n                params: {\n                    timeRange\n                }\n            }),\n        generateReport: (filters)=>apiClient.post('/progress/report', filters)\n    },\n    // Chat\n    chat: {\n        initialize: (trainerId)=>apiClient.post(`/chat/${trainerId}/initialize`),\n        sendMessage: (trainerId, data)=>apiClient.post(`/chat/${trainerId}/message`, data),\n        getHistory: (trainerId, params)=>apiClient.get(`/chat/${trainerId}/history`, {\n                params\n            })\n    },\n    // System\n    system: {\n        getHealth: ()=>apiClient.get('/system/health'),\n        getCacheStatus: ()=>apiClient.get('/system/cache/status'),\n        flushCache: ()=>apiClient.post('/system/cache/flush'),\n        // Add a simple connectivity test\n        ping: ()=>apiClient.get('/system/ping', {\n                timeout: 5000\n            })\n    },\n    // Convenience methods for easier usage\n    getTrainers: (params)=>apiClient.get('/trainers', {\n            params\n        }),\n    deleteTrainer: (id)=>apiClient.delete(`/trainers/${id}`)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(ssr)/./src/config/index.ts\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            console.log('Auth store: Starting login process');\n            set({\n                isLoading: true\n            });\n            try {\n                console.log('Auth store: Making API call');\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.login(credentials);\n                console.log('Auth store: API response received:', response.data);\n                const { user, token, role } = response.data.data;\n                // Store token in localStorage\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n                console.log('Auth store: Token stored in localStorage');\n                // Add role to user object if not present\n                const userWithRole = {\n                    ...user,\n                    role: user.role || role\n                };\n                const newState = {\n                    user: userWithRole,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                };\n                set(newState);\n                console.log('Auth store: State updated successfully', newState);\n                // Force a state check after update\n                const currentState = get();\n                console.log('Auth store: Current state after set:', currentState);\n            } catch (error) {\n                console.error('Auth store: Login failed', error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        loadUser: async ()=>{\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            if (!token) return;\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.getProfile();\n                const user = response.data.data.user;\n                // Ensure user has role field, default to 'member' if not present\n                const userWithRole = {\n                    ...user,\n                    role: user.role || 'member'\n                };\n                set({\n                    user: userWithRole,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch  {\n                // Token is invalid, clear it\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        setToken: (token)=>{\n            set({\n                token\n            });\n            if (token) {\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n            } else {\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        initialize: async ()=>{\n            console.log('Auth store: Initializing');\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            const storedState = localStorage.getItem('auth-store');\n            console.log('Auth store: Found token:', !!token);\n            console.log('Auth store: Found stored state:', !!storedState);\n            if (!token) {\n                console.log('Auth store: No token found in localStorage');\n                set({\n                    isLoading: false\n                }); // Make sure loading is false\n                return;\n            }\n            // If we have stored state, try to restore it first\n            if (storedState) {\n                try {\n                    const parsed = JSON.parse(storedState);\n                    if (parsed.state && parsed.state.user && parsed.state.isAuthenticated) {\n                        console.log('Auth store: Restoring auth state from localStorage');\n                        // Ensure user has role field, default to 'member' if not present\n                        const userWithRole = {\n                            ...parsed.state.user,\n                            role: parsed.state.user.role || 'member'\n                        };\n                        set({\n                            user: userWithRole,\n                            token: parsed.state.token || token,\n                            isAuthenticated: parsed.state.isAuthenticated,\n                            isLoading: false\n                        });\n                        return;\n                    }\n                } catch (error) {\n                    console.error('Auth store: Failed to parse stored state:', error);\n                }\n            }\n            console.log('Auth store: Token found, loading user profile');\n            try {\n                set({\n                    isLoading: true\n                });\n                await useAuthStore.getState().loadUser();\n            } catch (error) {\n                console.error('Auth store: Failed to load user during initialization', error);\n                // Clear invalid token\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                localStorage.removeItem('auth-store');\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }), {\n    name: 'auth-store',\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/@swc","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmember%2Fchat%2Fpage&page=%2Fmember%2Fchat%2Fpage&appPaths=%2Fmember%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fmember%2Fchat%2Fpage.tsx&appDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fpile%2Fmeet_trainer%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();