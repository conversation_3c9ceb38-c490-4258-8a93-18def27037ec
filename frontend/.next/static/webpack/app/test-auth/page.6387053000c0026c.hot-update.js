"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./src/app/test-auth/page.tsx":
/*!************************************!*\
  !*** ./src/app/test-auth/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TestAuth() {\n    _s();\n    const authState = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const [loginStatus, setLoginStatus] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [localStorageData, setLocalStorageData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        token: null,\n        authStore: null\n    });\n    // Handle client-side rendering\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TestAuth.useEffect\": ()=>{\n            setIsClient(true);\n            // Update localStorage data\n            const updateLocalStorageData = {\n                \"TestAuth.useEffect.updateLocalStorageData\": ()=>{\n                    setLocalStorageData({\n                        token: localStorage.getItem('auth_token'),\n                        authStore: localStorage.getItem('auth-store')\n                    });\n                }\n            }[\"TestAuth.useEffect.updateLocalStorageData\"];\n            updateLocalStorageData();\n            // Set up interval to refresh localStorage data\n            const interval = setInterval(updateLocalStorageData, 1000);\n            return ({\n                \"TestAuth.useEffect\": ()=>clearInterval(interval)\n            })[\"TestAuth.useEffect\"];\n        }\n    }[\"TestAuth.useEffect\"], []);\n    const testLogin = async ()=>{\n        try {\n            setLoginStatus('Logging in...');\n            // Clear any existing auth data\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('auth-store');\n            console.log('Test: Starting login');\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.auth.login({\n                email: '<EMAIL>',\n                password: '123456',\n                userType: 'member'\n            });\n            console.log('Test: Login API response:', response.data);\n            const { user, token } = response.data.data;\n            // Manually set auth state\n            authState.setUser(user);\n            authState.setToken(token);\n            console.log('Test: Auth state after manual set:', _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState());\n            setLoginStatus('Login successful! Check console for details.');\n        } catch (error) {\n            console.error('Test: Login failed:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            setLoginStatus('Login failed: ' + errorMessage);\n        }\n    };\n    const clearAuth = ()=>{\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('auth-store');\n        authState.logout();\n        setLoginStatus('Auth cleared');\n    };\n    const navigateToDashboard = ()=>{\n        window.location.href = '/member/dashboard';\n    };\n    // Show loading until client-side hydration\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold\",\n                    children: \"Auth Test Page\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"Current Auth State\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-xs overflow-auto bg-white p-2 rounded border max-h-64\",\n                                    children: JSON.stringify(authState, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"localStorage\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"auth_token:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                localStorageData.token ? 'Present' : 'None'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"auth-store:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                localStorageData.authStore ? 'Present' : 'None'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        localStorageData.token && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Token (first 50 chars):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"break-all bg-white p-1 rounded\",\n                                                    children: [\n                                                        localStorageData.token.substring(0, 50),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        localStorageData.authStore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stored State:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"bg-white p-1 rounded text-xs overflow-auto max-h-32\",\n                                                    children: JSON.stringify(JSON.parse(localStorageData.authStore), null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4 flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testLogin,\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                    children: \"Test Login (<EMAIL>)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearAuth,\n                                    className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700\",\n                                    children: \"Clear Auth\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: navigateToDashboard,\n                                    className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700\",\n                                    children: \"Go to Member Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        loginStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 p-3 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: loginStatus\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/auth/login\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Login Page\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/member/dashboard\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Member Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Home\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 p-3 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"Debug Info\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"isClient:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        isClient.toString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Current URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"User Agent:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        typeof navigator !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'SSR'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuth, \"ooE+noKnbk0npD2gvhV6oppiPnc=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore\n    ];\n});\n_c = TestAuth;\nvar _c;\n$RefreshReg$(_c, \"TestAuth\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-auth/page.tsx\n"));

/***/ })

});