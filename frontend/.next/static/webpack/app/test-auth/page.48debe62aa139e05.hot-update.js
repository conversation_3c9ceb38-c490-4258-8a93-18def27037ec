"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(app-pages-browser)/./src/config/index.ts\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            console.log('Auth store: Starting login process');\n            set({\n                isLoading: true\n            });\n            try {\n                console.log('Auth store: Making API call');\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.login(credentials);\n                console.log('Auth store: API response received:', response.data);\n                const { user, token, role } = response.data.data;\n                // Store token in localStorage\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n                console.log('Auth store: Token stored in localStorage');\n                // Add role to user object if not present\n                const userWithRole = {\n                    ...user,\n                    role: user.role || role\n                };\n                const newState = {\n                    user: userWithRole,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                };\n                set(newState);\n                console.log('Auth store: State updated successfully', newState);\n                // Force a state check after update\n                const currentState = get();\n                console.log('Auth store: Current state after set:', currentState);\n            } catch (error) {\n                console.error('Auth store: Login failed', error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        loadUser: async ()=>{\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            if (!token) return;\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.getProfile();\n                const user = response.data.data.user;\n                set({\n                    user,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (e) {\n                // Token is invalid, clear it\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        setToken: (token)=>{\n            set({\n                token\n            });\n            if (token) {\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n            } else {\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        initialize: async ()=>{\n            console.log('Auth store: Initializing');\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            const storedState = localStorage.getItem('auth-store');\n            console.log('Auth store: Found token:', !!token);\n            console.log('Auth store: Found stored state:', !!storedState);\n            if (!token) {\n                console.log('Auth store: No token found in localStorage');\n                set({\n                    isLoading: false\n                }); // Make sure loading is false\n                return;\n            }\n            // If we have stored state, try to restore it first\n            if (storedState) {\n                try {\n                    const parsed = JSON.parse(storedState);\n                    if (parsed.state && parsed.state.user && parsed.state.isAuthenticated) {\n                        console.log('Auth store: Restoring auth state from localStorage');\n                        set({\n                            user: parsed.state.user,\n                            token: parsed.state.token || token,\n                            isAuthenticated: parsed.state.isAuthenticated,\n                            isLoading: false\n                        });\n                        return;\n                    }\n                } catch (error) {\n                    console.error('Auth store: Failed to parse stored state:', error);\n                }\n            }\n            console.log('Auth store: Token found, loading user profile');\n            try {\n                set({\n                    isLoading: true\n                });\n                await useAuthStore.getState().loadUser();\n            } catch (error) {\n                console.error('Auth store: Failed to load user during initialization', error);\n                // Clear invalid token\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                localStorage.removeItem('auth-store');\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }), {\n    name: 'auth-store',\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlDO0FBQ1k7QUFDYjtBQUVGO0FBWXZCLE1BQU1JLGVBQWVKLCtDQUFNQSxHQUNoQ0MsMkRBQU9BLENBQ0wsQ0FBQ0ksS0FBS0MsTUFBUztRQUNiQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsaUJBQWlCO1FBQ2pCQyxXQUFXO1FBRVhDLE9BQU8sT0FBT0M7WUFDWkMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pULElBQUk7Z0JBQUVLLFdBQVc7WUFBSztZQUN0QixJQUFJO2dCQUNGRyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTUMsV0FBVyxNQUFNYix5Q0FBR0EsQ0FBQ2MsSUFBSSxDQUFDTCxLQUFLLENBQUNDO2dCQUN0Q0MsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQ0MsU0FBU0UsSUFBSTtnQkFDL0QsTUFBTSxFQUFFVixJQUFJLEVBQUVDLEtBQUssRUFBRVUsSUFBSSxFQUFFLEdBQUdILFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFFaEQsOEJBQThCO2dCQUM5QkUsYUFBYUMsT0FBTyxDQUFDakIsK0NBQU1BLENBQUNhLElBQUksQ0FBQ0ssUUFBUSxFQUFFYjtnQkFDM0NLLFFBQVFDLEdBQUcsQ0FBQztnQkFFWix5Q0FBeUM7Z0JBQ3pDLE1BQU1RLGVBQWU7b0JBQUUsR0FBR2YsSUFBSTtvQkFBRVcsTUFBTVgsS0FBS1csSUFBSSxJQUFJQTtnQkFBSztnQkFFeEQsTUFBTUssV0FBVztvQkFDZmhCLE1BQU1lO29CQUNOZDtvQkFDQUMsaUJBQWlCO29CQUNqQkMsV0FBVztnQkFDYjtnQkFFQUwsSUFBSWtCO2dCQUNKVixRQUFRQyxHQUFHLENBQUMsMENBQTBDUztnQkFFdEQsbUNBQW1DO2dCQUNuQyxNQUFNQyxlQUFlbEI7Z0JBQ3JCTyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDVTtZQUN0RCxFQUFFLE9BQU9DLE9BQU87Z0JBQ2RaLFFBQVFZLEtBQUssQ0FBQyw0QkFBNEJBO2dCQUMxQ3BCLElBQUk7b0JBQUVLLFdBQVc7Z0JBQU07Z0JBQ3ZCLE1BQU1lO1lBQ1I7UUFDRjtRQUVBQyxRQUFRO1lBQ05QLGFBQWFRLFVBQVUsQ0FBQ3hCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVE7WUFDNUNoQixJQUFJO2dCQUNGRSxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxpQkFBaUI7Z0JBQ2pCQyxXQUFXO1lBQ2I7UUFDRjtRQUVBa0IsVUFBVTtZQUNSLE1BQU1wQixRQUFRVyxhQUFhVSxPQUFPLENBQUMxQiwrQ0FBTUEsQ0FBQ2EsSUFBSSxDQUFDSyxRQUFRO1lBQ3ZELElBQUksQ0FBQ2IsT0FBTztZQUVaSCxJQUFJO2dCQUFFSyxXQUFXO1lBQUs7WUFDdEIsSUFBSTtnQkFDRixNQUFNSyxXQUFXLE1BQU1iLHlDQUFHQSxDQUFDYyxJQUFJLENBQUNjLFVBQVU7Z0JBQzFDLE1BQU12QixPQUFPUSxTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ1YsSUFBSTtnQkFFcENGLElBQUk7b0JBQ0ZFO29CQUNBQztvQkFDQUMsaUJBQWlCO29CQUNqQkMsV0FBVztnQkFDYjtZQUNGLEVBQUUsVUFBTTtnQkFDTiw2QkFBNkI7Z0JBQzdCUyxhQUFhUSxVQUFVLENBQUN4QiwrQ0FBTUEsQ0FBQ2EsSUFBSSxDQUFDSyxRQUFRO2dCQUM1Q2hCLElBQUk7b0JBQ0ZFLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLGlCQUFpQjtvQkFDakJDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUFxQixTQUFTLENBQUN4QjtZQUNSRixJQUFJO2dCQUFFRTtnQkFBTUUsaUJBQWlCLENBQUMsQ0FBQ0Y7WUFBSztRQUN0QztRQUVBeUIsVUFBVSxDQUFDeEI7WUFDVEgsSUFBSTtnQkFBRUc7WUFBTTtZQUNaLElBQUlBLE9BQU87Z0JBQ1RXLGFBQWFDLE9BQU8sQ0FBQ2pCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVEsRUFBRWI7WUFDN0MsT0FBTztnQkFDTFcsYUFBYVEsVUFBVSxDQUFDeEIsK0NBQU1BLENBQUNhLElBQUksQ0FBQ0ssUUFBUTtZQUM5QztRQUNGO1FBRUFZLFlBQVksQ0FBQ0M7WUFDWDdCLElBQUk7Z0JBQUVLLFdBQVd3QjtZQUFRO1FBQzNCO1FBRUFDLFlBQVk7WUFDVnRCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1OLFFBQVFXLGFBQWFVLE9BQU8sQ0FBQzFCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVE7WUFDdkQsTUFBTWUsY0FBY2pCLGFBQWFVLE9BQU8sQ0FBQztZQUV6Q2hCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDTjtZQUMxQ0ssUUFBUUMsR0FBRyxDQUFDLG1DQUFtQyxDQUFDLENBQUNzQjtZQUVqRCxJQUFJLENBQUM1QixPQUFPO2dCQUNWSyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pULElBQUk7b0JBQUVLLFdBQVc7Z0JBQU0sSUFBSSw2QkFBNkI7Z0JBQ3hEO1lBQ0Y7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSTBCLGFBQWE7Z0JBQ2YsSUFBSTtvQkFDRixNQUFNQyxTQUFTQyxLQUFLQyxLQUFLLENBQUNIO29CQUMxQixJQUFJQyxPQUFPRyxLQUFLLElBQUlILE9BQU9HLEtBQUssQ0FBQ2pDLElBQUksSUFBSThCLE9BQU9HLEtBQUssQ0FBQy9CLGVBQWUsRUFBRTt3QkFDckVJLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWlQsSUFBSTs0QkFDRkUsTUFBTThCLE9BQU9HLEtBQUssQ0FBQ2pDLElBQUk7NEJBQ3ZCQyxPQUFPNkIsT0FBT0csS0FBSyxDQUFDaEMsS0FBSyxJQUFJQTs0QkFDN0JDLGlCQUFpQjRCLE9BQU9HLEtBQUssQ0FBQy9CLGVBQWU7NEJBQzdDQyxXQUFXO3dCQUNiO3dCQUNBO29CQUNGO2dCQUNGLEVBQUUsT0FBT2UsT0FBTztvQkFDZFosUUFBUVksS0FBSyxDQUFDLDZDQUE2Q0E7Z0JBQzdEO1lBQ0Y7WUFFQVosUUFBUUMsR0FBRyxDQUFDO1lBQ1osSUFBSTtnQkFDRlQsSUFBSTtvQkFBRUssV0FBVztnQkFBSztnQkFDdEIsTUFBTU4sYUFBYXFDLFFBQVEsR0FBR2IsUUFBUTtZQUN4QyxFQUFFLE9BQU9ILE9BQU87Z0JBQ2RaLFFBQVFZLEtBQUssQ0FBQyx5REFBeURBO2dCQUN2RSxzQkFBc0I7Z0JBQ3RCTixhQUFhUSxVQUFVLENBQUN4QiwrQ0FBTUEsQ0FBQ2EsSUFBSSxDQUFDSyxRQUFRO2dCQUM1Q0YsYUFBYVEsVUFBVSxDQUFDO2dCQUN4QnRCLElBQUk7b0JBQ0ZFLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLGlCQUFpQjtvQkFDakJDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO0lBQ0YsSUFDQTtJQUNFZ0MsTUFBTTtJQUNOQyxZQUFZLENBQUNILFFBQVc7WUFDdEJqQyxNQUFNaUMsTUFBTWpDLElBQUk7WUFDaEJDLE9BQU9nQyxNQUFNaEMsS0FBSztZQUNsQkMsaUJBQWlCK0IsTUFBTS9CLGVBQWU7UUFDeEM7QUFDRixJQUVGIiwic291cmNlcyI6WyIvaG9tZS9waWxlL21lZXRfdHJhaW5lci9mcm9udGVuZC9zcmMvc3RvcmUvYXV0aC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IHBlcnNpc3QgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnO1xuaW1wb3J0IHsgYXBpIH0gZnJvbSAnQC9saWIvYXBpJztcbmltcG9ydCB7IFVzZXIsIExvZ2luQ3JlZGVudGlhbHMsIEF1dGhTdGF0ZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IGNvbmZpZyBmcm9tICdAL2NvbmZpZyc7XG5cbmludGVyZmFjZSBBdXRoU3RvcmUgZXh0ZW5kcyBBdXRoU3RhdGUge1xuICBsb2dpbjogKGNyZWRlbnRpYWxzOiBMb2dpbkNyZWRlbnRpYWxzKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBsb2dvdXQ6ICgpID0+IHZvaWQ7XG4gIGxvYWRVc2VyOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBzZXRVc2VyOiAodXNlcjogVXNlciB8IG51bGwpID0+IHZvaWQ7XG4gIHNldFRva2VuOiAodG9rZW46IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG4gIHNldExvYWRpbmc6IChsb2FkaW5nOiBib29sZWFuKSA9PiB2b2lkO1xuICBpbml0aWFsaXplOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5leHBvcnQgY29uc3QgdXNlQXV0aFN0b3JlID0gY3JlYXRlPEF1dGhTdG9yZT4oKShcbiAgcGVyc2lzdChcbiAgICAoc2V0LCBnZXQpID0+ICh7XG4gICAgICB1c2VyOiBudWxsLFxuICAgICAgdG9rZW46IG51bGwsXG4gICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcblxuICAgICAgbG9naW46IGFzeW5jIChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscykgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogU3RhcnRpbmcgbG9naW4gcHJvY2VzcycpO1xuICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IE1ha2luZyBBUEkgY2FsbCcpO1xuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmF1dGgubG9naW4oY3JlZGVudGlhbHMpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBBUEkgcmVzcG9uc2UgcmVjZWl2ZWQ6JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgY29uc3QgeyB1c2VyLCB0b2tlbiwgcm9sZSB9ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIFN0b3JlIHRva2VuIGluIGxvY2FsU3RvcmFnZVxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGNvbmZpZy5hdXRoLnRva2VuS2V5LCB0b2tlbik7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IFRva2VuIHN0b3JlZCBpbiBsb2NhbFN0b3JhZ2UnKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBBZGQgcm9sZSB0byB1c2VyIG9iamVjdCBpZiBub3QgcHJlc2VudFxuICAgICAgICAgIGNvbnN0IHVzZXJXaXRoUm9sZSA9IHsgLi4udXNlciwgcm9sZTogdXNlci5yb2xlIHx8IHJvbGUgfTtcbiAgICAgICAgICBcbiAgICAgICAgICBjb25zdCBuZXdTdGF0ZSA9IHtcbiAgICAgICAgICAgIHVzZXI6IHVzZXJXaXRoUm9sZSxcbiAgICAgICAgICAgIHRva2VuLFxuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9O1xuICAgICAgICAgIFxuICAgICAgICAgIHNldChuZXdTdGF0ZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IFN0YXRlIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JywgbmV3U3RhdGUpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIEZvcmNlIGEgc3RhdGUgY2hlY2sgYWZ0ZXIgdXBkYXRlXG4gICAgICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gZ2V0KCk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IEN1cnJlbnQgc3RhdGUgYWZ0ZXIgc2V0OicsIGN1cnJlbnRTdGF0ZSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignQXV0aCBzdG9yZTogTG9naW4gZmFpbGVkJywgZXJyb3IpO1xuICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICAgIH0sXG5cbiAgICAgIGxvZ291dDogKCkgPT4ge1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShjb25maWcuYXV0aC50b2tlbktleSk7XG4gICAgICAgIHNldCh7XG4gICAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgICB0b2tlbjogbnVsbCxcbiAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgICAgfSxcblxuICAgICAgbG9hZFVzZXI6IGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShjb25maWcuYXV0aC50b2tlbktleSk7XG4gICAgICAgIGlmICghdG9rZW4pIHJldHVybjtcblxuICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuYXV0aC5nZXRQcm9maWxlKCk7XG4gICAgICAgICAgY29uc3QgdXNlciA9IHJlc3BvbnNlLmRhdGEuZGF0YS51c2VyO1xuICAgICAgICAgIFxuICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICB1c2VyLFxuICAgICAgICAgICAgdG9rZW4sXG4gICAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHRydWUsXG4gICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGNhdGNoIHtcbiAgICAgICAgICAvLyBUb2tlbiBpcyBpbnZhbGlkLCBjbGVhciBpdFxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGNvbmZpZy5hdXRoLnRva2VuS2V5KTtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgICAgIHRva2VuOiBudWxsLFxuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG5cbiAgICAgIHNldFVzZXI6ICh1c2VyOiBVc2VyIHwgbnVsbCkgPT4ge1xuICAgICAgICBzZXQoeyB1c2VyLCBpc0F1dGhlbnRpY2F0ZWQ6ICEhdXNlciB9KTtcbiAgICAgIH0sXG5cbiAgICAgIHNldFRva2VuOiAodG9rZW46IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICAgICAgc2V0KHsgdG9rZW4gfSk7XG4gICAgICAgIGlmICh0b2tlbikge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGNvbmZpZy5hdXRoLnRva2VuS2V5LCB0b2tlbik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY29uZmlnLmF1dGgudG9rZW5LZXkpO1xuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICBzZXRMb2FkaW5nOiAobG9hZGluZzogYm9vbGVhbikgPT4ge1xuICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IGxvYWRpbmcgfSk7XG4gICAgICB9LFxuXG4gICAgICBpbml0aWFsaXplOiBhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBJbml0aWFsaXppbmcnKTtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShjb25maWcuYXV0aC50b2tlbktleSk7XG4gICAgICAgIGNvbnN0IHN0b3JlZFN0YXRlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGgtc3RvcmUnKTtcbiAgICAgICAgXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBGb3VuZCB0b2tlbjonLCAhIXRva2VuKTtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IEZvdW5kIHN0b3JlZCBzdGF0ZTonLCAhIXN0b3JlZFN0YXRlKTtcbiAgICAgICAgXG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogTm8gdG9rZW4gZm91bmQgaW4gbG9jYWxTdG9yYWdlJyk7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiBmYWxzZSB9KTsgLy8gTWFrZSBzdXJlIGxvYWRpbmcgaXMgZmFsc2VcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIElmIHdlIGhhdmUgc3RvcmVkIHN0YXRlLCB0cnkgdG8gcmVzdG9yZSBpdCBmaXJzdFxuICAgICAgICBpZiAoc3RvcmVkU3RhdGUpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShzdG9yZWRTdGF0ZSk7XG4gICAgICAgICAgICBpZiAocGFyc2VkLnN0YXRlICYmIHBhcnNlZC5zdGF0ZS51c2VyICYmIHBhcnNlZC5zdGF0ZS5pc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IFJlc3RvcmluZyBhdXRoIHN0YXRlIGZyb20gbG9jYWxTdG9yYWdlJyk7XG4gICAgICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICAgICAgdXNlcjogcGFyc2VkLnN0YXRlLnVzZXIsXG4gICAgICAgICAgICAgICAgdG9rZW46IHBhcnNlZC5zdGF0ZS50b2tlbiB8fCB0b2tlbixcbiAgICAgICAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHBhcnNlZC5zdGF0ZS5pc0F1dGhlbnRpY2F0ZWQsXG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoIHN0b3JlOiBGYWlsZWQgdG8gcGFyc2Ugc3RvcmVkIHN0YXRlOicsIGVycm9yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBUb2tlbiBmb3VuZCwgbG9hZGluZyB1c2VyIHByb2ZpbGUnKTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSk7XG4gICAgICAgICAgYXdhaXQgdXNlQXV0aFN0b3JlLmdldFN0YXRlKCkubG9hZFVzZXIoKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRoIHN0b3JlOiBGYWlsZWQgdG8gbG9hZCB1c2VyIGR1cmluZyBpbml0aWFsaXphdGlvbicsIGVycm9yKTtcbiAgICAgICAgICAvLyBDbGVhciBpbnZhbGlkIHRva2VuXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY29uZmlnLmF1dGgudG9rZW5LZXkpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoLXN0b3JlJyk7XG4gICAgICAgICAgc2V0KHtcbiAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICB0b2tlbjogbnVsbCxcbiAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSksXG4gICAge1xuICAgICAgbmFtZTogJ2F1dGgtc3RvcmUnLFxuICAgICAgcGFydGlhbGl6ZTogKHN0YXRlKSA9PiAoe1xuICAgICAgICB1c2VyOiBzdGF0ZS51c2VyLFxuICAgICAgICB0b2tlbjogc3RhdGUudG9rZW4sXG4gICAgICAgIGlzQXV0aGVudGljYXRlZDogc3RhdGUuaXNBdXRoZW50aWNhdGVkLFxuICAgICAgfSksXG4gICAgfVxuICApXG4pOyAiXSwibmFtZXMiOlsiY3JlYXRlIiwicGVyc2lzdCIsImFwaSIsImNvbmZpZyIsInVzZUF1dGhTdG9yZSIsInNldCIsImdldCIsInVzZXIiLCJ0b2tlbiIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsImxvZ2luIiwiY3JlZGVudGlhbHMiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJhdXRoIiwiZGF0YSIsInJvbGUiLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwidG9rZW5LZXkiLCJ1c2VyV2l0aFJvbGUiLCJuZXdTdGF0ZSIsImN1cnJlbnRTdGF0ZSIsImVycm9yIiwibG9nb3V0IiwicmVtb3ZlSXRlbSIsImxvYWRVc2VyIiwiZ2V0SXRlbSIsImdldFByb2ZpbGUiLCJzZXRVc2VyIiwic2V0VG9rZW4iLCJzZXRMb2FkaW5nIiwibG9hZGluZyIsImluaXRpYWxpemUiLCJzdG9yZWRTdGF0ZSIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsInN0YXRlIiwiZ2V0U3RhdGUiLCJuYW1lIiwicGFydGlhbGl6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});