"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./src/app/test-auth/page.tsx":
/*!************************************!*\
  !*** ./src/app/test-auth/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TestAuth() {\n    _s();\n    const authState = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const [loginStatus, setLoginStatus] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [localStorageData, setLocalStorageData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        token: null,\n        authStore: null\n    });\n    // Handle client-side rendering\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TestAuth.useEffect\": ()=>{\n            setIsClient(true);\n            // Update localStorage data\n            const updateLocalStorageData = {\n                \"TestAuth.useEffect.updateLocalStorageData\": ()=>{\n                    setLocalStorageData({\n                        token: localStorage.getItem('auth_token'),\n                        authStore: localStorage.getItem('auth-store')\n                    });\n                }\n            }[\"TestAuth.useEffect.updateLocalStorageData\"];\n            updateLocalStorageData();\n            // Set up interval to refresh localStorage data\n            const interval = setInterval(updateLocalStorageData, 1000);\n            return ({\n                \"TestAuth.useEffect\": ()=>clearInterval(interval)\n            })[\"TestAuth.useEffect\"];\n        }\n    }[\"TestAuth.useEffect\"], []);\n    const testLogin = async ()=>{\n        try {\n            setLoginStatus('Logging in...');\n            // Clear any existing auth data\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('auth-store');\n            console.log('Test: Starting login');\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.api.auth.login({\n                email: '<EMAIL>',\n                password: '123456',\n                userType: 'member'\n            });\n            console.log('Test: Login API response:', response.data);\n            const { user, token } = response.data.data;\n            // Manually set auth state\n            authState.setUser(user);\n            authState.setToken(token);\n            console.log('Test: Auth state after manual set:', _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState());\n            setLoginStatus('Login successful! Check console for details.');\n        } catch (error) {\n            console.error('Test: Login failed:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            setLoginStatus('Login failed: ' + errorMessage);\n        }\n    };\n    const clearAuth = ()=>{\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('auth-store');\n        authState.logout();\n        setLoginStatus('Auth cleared');\n    };\n    const navigateToDashboard = ()=>{\n        window.location.href = '/member/dashboard';\n    };\n    // Show loading until client-side hydration\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold\",\n                    children: \"Auth Test Page\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"Current Auth State\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-xs overflow-auto bg-white p-2 rounded border max-h-64\",\n                                    children: JSON.stringify(authState, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"localStorage\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"auth_token:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                localStorageData.token ? 'Present' : 'None'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"auth-store:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                localStorageData.authStore ? 'Present' : 'None'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        localStorageData.token && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Token (first 50 chars):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"break-all bg-white p-1 rounded\",\n                                                    children: [\n                                                        localStorageData.token.substring(0, 50),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        localStorageData.authStore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stored State:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"bg-white p-1 rounded text-xs overflow-auto max-h-32\",\n                                                    children: JSON.stringify(JSON.parse(localStorageData.authStore), null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4 flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testLogin,\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                    children: \"Test Login (<EMAIL>)\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearAuth,\n                                    className: \"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700\",\n                                    children: \"Clear Auth\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: navigateToDashboard,\n                                    className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700\",\n                                    children: \"Go to Member Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        loginStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 p-3 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: loginStatus\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/auth/login\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Login Page\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/member/dashboard\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Member Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"text-blue-600 hover:underline\",\n                                children: \"Go to Home\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 14\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 p-3 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold mb-2\",\n                            children: \"Debug Info\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"isClient:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        isClient.toString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Current URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"User Agent:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" \",\n                                        typeof navigator !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'SSR'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/test-auth/page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuth, \"ooE+noKnbk0npD2gvhV6oppiPnc=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore\n    ];\n});\n_c = TestAuth;\nvar _c;\n$RefreshReg$(_c, \"TestAuth\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-auth/page.tsx\n"));

/***/ })

});