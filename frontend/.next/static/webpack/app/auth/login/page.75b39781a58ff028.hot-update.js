"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(app-pages-browser)/./src/config/index.ts\");\n\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (credentials)=>{\n            console.log('Auth store: Starting login process');\n            set({\n                isLoading: true\n            });\n            try {\n                console.log('Auth store: Making API call');\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.login(credentials);\n                console.log('Auth store: API response received:', response.data);\n                const { user, token, role } = response.data.data;\n                // Store token in localStorage\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n                console.log('Auth store: Token stored in localStorage');\n                // Add role to user object if not present\n                const userWithRole = {\n                    ...user,\n                    role: user.role || role\n                };\n                const newState = {\n                    user: userWithRole,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                };\n                set(newState);\n                console.log('Auth store: State updated successfully', newState);\n                // Force a state check after update\n                const currentState = get();\n                console.log('Auth store: Current state after set:', currentState);\n            } catch (error) {\n                console.error('Auth store: Login failed', error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        loadUser: async ()=>{\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            if (!token) return;\n            set({\n                isLoading: true\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.api.auth.getProfile();\n                const user = response.data.data.user;\n                set({\n                    user,\n                    token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (e) {\n                // Token is invalid, clear it\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        setToken: (token)=>{\n            set({\n                token\n            });\n            if (token) {\n                localStorage.setItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey, token);\n            } else {\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            }\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        initialize: async ()=>{\n            console.log('Auth store: Initializing');\n            const token = localStorage.getItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n            const storedState = localStorage.getItem('auth-store');\n            console.log('Auth store: Found token:', !!token);\n            console.log('Auth store: Found stored state:', !!storedState);\n            if (!token) {\n                console.log('Auth store: No token found in localStorage');\n                set({\n                    isLoading: false\n                }); // Make sure loading is false\n                return;\n            }\n            // If we have stored state, try to restore it first\n            if (storedState) {\n                try {\n                    const parsed = JSON.parse(storedState);\n                    if (parsed.state && parsed.state.user && parsed.state.isAuthenticated) {\n                        console.log('Auth store: Restoring auth state from localStorage');\n                        // Ensure user has role field, default to 'member' if not present\n                        const userWithRole = {\n                            ...parsed.state.user,\n                            role: parsed.state.user.role || 'member'\n                        };\n                        set({\n                            user: userWithRole,\n                            token: parsed.state.token || token,\n                            isAuthenticated: parsed.state.isAuthenticated,\n                            isLoading: false\n                        });\n                        return;\n                    }\n                } catch (error) {\n                    console.error('Auth store: Failed to parse stored state:', error);\n                }\n            }\n            console.log('Auth store: Token found, loading user profile');\n            try {\n                set({\n                    isLoading: true\n                });\n                await useAuthStore.getState().loadUser();\n            } catch (error) {\n                console.error('Auth store: Failed to load user during initialization', error);\n                // Clear invalid token\n                localStorage.removeItem(_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"].auth.tokenKey);\n                localStorage.removeItem('auth-store');\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }), {\n    name: 'auth-store',\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlDO0FBQ1k7QUFDYjtBQUVGO0FBWXZCLE1BQU1JLGVBQWVKLCtDQUFNQSxHQUNoQ0MsMkRBQU9BLENBQ0wsQ0FBQ0ksS0FBS0MsTUFBUztRQUNiQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsaUJBQWlCO1FBQ2pCQyxXQUFXO1FBRVhDLE9BQU8sT0FBT0M7WUFDWkMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pULElBQUk7Z0JBQUVLLFdBQVc7WUFBSztZQUN0QixJQUFJO2dCQUNGRyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTUMsV0FBVyxNQUFNYix5Q0FBR0EsQ0FBQ2MsSUFBSSxDQUFDTCxLQUFLLENBQUNDO2dCQUN0Q0MsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQ0MsU0FBU0UsSUFBSTtnQkFDL0QsTUFBTSxFQUFFVixJQUFJLEVBQUVDLEtBQUssRUFBRVUsSUFBSSxFQUFFLEdBQUdILFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFFaEQsOEJBQThCO2dCQUM5QkUsYUFBYUMsT0FBTyxDQUFDakIsK0NBQU1BLENBQUNhLElBQUksQ0FBQ0ssUUFBUSxFQUFFYjtnQkFDM0NLLFFBQVFDLEdBQUcsQ0FBQztnQkFFWix5Q0FBeUM7Z0JBQ3pDLE1BQU1RLGVBQWU7b0JBQUUsR0FBR2YsSUFBSTtvQkFBRVcsTUFBTVgsS0FBS1csSUFBSSxJQUFJQTtnQkFBSztnQkFFeEQsTUFBTUssV0FBVztvQkFDZmhCLE1BQU1lO29CQUNOZDtvQkFDQUMsaUJBQWlCO29CQUNqQkMsV0FBVztnQkFDYjtnQkFFQUwsSUFBSWtCO2dCQUNKVixRQUFRQyxHQUFHLENBQUMsMENBQTBDUztnQkFFdEQsbUNBQW1DO2dCQUNuQyxNQUFNQyxlQUFlbEI7Z0JBQ3JCTyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDVTtZQUN0RCxFQUFFLE9BQU9DLE9BQU87Z0JBQ2RaLFFBQVFZLEtBQUssQ0FBQyw0QkFBNEJBO2dCQUMxQ3BCLElBQUk7b0JBQUVLLFdBQVc7Z0JBQU07Z0JBQ3ZCLE1BQU1lO1lBQ1I7UUFDRjtRQUVBQyxRQUFRO1lBQ05QLGFBQWFRLFVBQVUsQ0FBQ3hCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVE7WUFDNUNoQixJQUFJO2dCQUNGRSxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxpQkFBaUI7Z0JBQ2pCQyxXQUFXO1lBQ2I7UUFDRjtRQUVBa0IsVUFBVTtZQUNSLE1BQU1wQixRQUFRVyxhQUFhVSxPQUFPLENBQUMxQiwrQ0FBTUEsQ0FBQ2EsSUFBSSxDQUFDSyxRQUFRO1lBQ3ZELElBQUksQ0FBQ2IsT0FBTztZQUVaSCxJQUFJO2dCQUFFSyxXQUFXO1lBQUs7WUFDdEIsSUFBSTtnQkFDRixNQUFNSyxXQUFXLE1BQU1iLHlDQUFHQSxDQUFDYyxJQUFJLENBQUNjLFVBQVU7Z0JBQzFDLE1BQU12QixPQUFPUSxTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ1YsSUFBSTtnQkFFcENGLElBQUk7b0JBQ0ZFO29CQUNBQztvQkFDQUMsaUJBQWlCO29CQUNqQkMsV0FBVztnQkFDYjtZQUNGLEVBQUUsVUFBTTtnQkFDTiw2QkFBNkI7Z0JBQzdCUyxhQUFhUSxVQUFVLENBQUN4QiwrQ0FBTUEsQ0FBQ2EsSUFBSSxDQUFDSyxRQUFRO2dCQUM1Q2hCLElBQUk7b0JBQ0ZFLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLGlCQUFpQjtvQkFDakJDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUFxQixTQUFTLENBQUN4QjtZQUNSRixJQUFJO2dCQUFFRTtnQkFBTUUsaUJBQWlCLENBQUMsQ0FBQ0Y7WUFBSztRQUN0QztRQUVBeUIsVUFBVSxDQUFDeEI7WUFDVEgsSUFBSTtnQkFBRUc7WUFBTTtZQUNaLElBQUlBLE9BQU87Z0JBQ1RXLGFBQWFDLE9BQU8sQ0FBQ2pCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVEsRUFBRWI7WUFDN0MsT0FBTztnQkFDTFcsYUFBYVEsVUFBVSxDQUFDeEIsK0NBQU1BLENBQUNhLElBQUksQ0FBQ0ssUUFBUTtZQUM5QztRQUNGO1FBRUFZLFlBQVksQ0FBQ0M7WUFDWDdCLElBQUk7Z0JBQUVLLFdBQVd3QjtZQUFRO1FBQzNCO1FBRUFDLFlBQVk7WUFDVnRCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1OLFFBQVFXLGFBQWFVLE9BQU8sQ0FBQzFCLCtDQUFNQSxDQUFDYSxJQUFJLENBQUNLLFFBQVE7WUFDdkQsTUFBTWUsY0FBY2pCLGFBQWFVLE9BQU8sQ0FBQztZQUV6Q2hCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDTjtZQUMxQ0ssUUFBUUMsR0FBRyxDQUFDLG1DQUFtQyxDQUFDLENBQUNzQjtZQUVqRCxJQUFJLENBQUM1QixPQUFPO2dCQUNWSyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pULElBQUk7b0JBQUVLLFdBQVc7Z0JBQU0sSUFBSSw2QkFBNkI7Z0JBQ3hEO1lBQ0Y7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSTBCLGFBQWE7Z0JBQ2YsSUFBSTtvQkFDRixNQUFNQyxTQUFTQyxLQUFLQyxLQUFLLENBQUNIO29CQUMxQixJQUFJQyxPQUFPRyxLQUFLLElBQUlILE9BQU9HLEtBQUssQ0FBQ2pDLElBQUksSUFBSThCLE9BQU9HLEtBQUssQ0FBQy9CLGVBQWUsRUFBRTt3QkFDckVJLFFBQVFDLEdBQUcsQ0FBQzt3QkFFWixpRUFBaUU7d0JBQ2pFLE1BQU1RLGVBQWU7NEJBQ25CLEdBQUdlLE9BQU9HLEtBQUssQ0FBQ2pDLElBQUk7NEJBQ3BCVyxNQUFNbUIsT0FBT0csS0FBSyxDQUFDakMsSUFBSSxDQUFDVyxJQUFJLElBQUk7d0JBQ2xDO3dCQUVBYixJQUFJOzRCQUNGRSxNQUFNZTs0QkFDTmQsT0FBTzZCLE9BQU9HLEtBQUssQ0FBQ2hDLEtBQUssSUFBSUE7NEJBQzdCQyxpQkFBaUI0QixPQUFPRyxLQUFLLENBQUMvQixlQUFlOzRCQUM3Q0MsV0FBVzt3QkFDYjt3QkFDQTtvQkFDRjtnQkFDRixFQUFFLE9BQU9lLE9BQU87b0JBQ2RaLFFBQVFZLEtBQUssQ0FBQyw2Q0FBNkNBO2dCQUM3RDtZQUNGO1lBRUFaLFFBQVFDLEdBQUcsQ0FBQztZQUNaLElBQUk7Z0JBQ0ZULElBQUk7b0JBQUVLLFdBQVc7Z0JBQUs7Z0JBQ3RCLE1BQU1OLGFBQWFxQyxRQUFRLEdBQUdiLFFBQVE7WUFDeEMsRUFBRSxPQUFPSCxPQUFPO2dCQUNkWixRQUFRWSxLQUFLLENBQUMseURBQXlEQTtnQkFDdkUsc0JBQXNCO2dCQUN0Qk4sYUFBYVEsVUFBVSxDQUFDeEIsK0NBQU1BLENBQUNhLElBQUksQ0FBQ0ssUUFBUTtnQkFDNUNGLGFBQWFRLFVBQVUsQ0FBQztnQkFDeEJ0QixJQUFJO29CQUNGRSxNQUFNO29CQUNOQyxPQUFPO29CQUNQQyxpQkFBaUI7b0JBQ2pCQyxXQUFXO2dCQUNiO1lBQ0Y7UUFDRjtJQUNGLElBQ0E7SUFDRWdDLE1BQU07SUFDTkMsWUFBWSxDQUFDSCxRQUFXO1lBQ3RCakMsTUFBTWlDLE1BQU1qQyxJQUFJO1lBQ2hCQyxPQUFPZ0MsTUFBTWhDLEtBQUs7WUFDbEJDLGlCQUFpQitCLE1BQU0vQixlQUFlO1FBQ3hDO0FBQ0YsSUFFRiIsInNvdXJjZXMiOlsiL2hvbWUvcGlsZS9tZWV0X3RyYWluZXIvZnJvbnRlbmQvc3JjL3N0b3JlL2F1dGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XG5pbXBvcnQgeyBwZXJzaXN0IH0gZnJvbSAnenVzdGFuZC9taWRkbGV3YXJlJztcbmltcG9ydCB7IGFwaSB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgeyBVc2VyLCBMb2dpbkNyZWRlbnRpYWxzLCBBdXRoU3RhdGUgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCBjb25maWcgZnJvbSAnQC9jb25maWcnO1xuXG5pbnRlcmZhY2UgQXV0aFN0b3JlIGV4dGVuZHMgQXV0aFN0YXRlIHtcbiAgbG9naW46IChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xuICBsb2FkVXNlcjogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgc2V0VXNlcjogKHVzZXI6IFVzZXIgfCBudWxsKSA9PiB2b2lkO1xuICBzZXRUb2tlbjogKHRva2VuOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkO1xuICBzZXRMb2FkaW5nOiAobG9hZGluZzogYm9vbGVhbikgPT4gdm9pZDtcbiAgaW5pdGlhbGl6ZTogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuZXhwb3J0IGNvbnN0IHVzZUF1dGhTdG9yZSA9IGNyZWF0ZTxBdXRoU3RvcmU+KCkoXG4gIHBlcnNpc3QoXG4gICAgKHNldCwgZ2V0KSA9PiAoe1xuICAgICAgdXNlcjogbnVsbCxcbiAgICAgIHRva2VuOiBudWxsLFxuICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgIGlzTG9hZGluZzogZmFsc2UsXG5cbiAgICAgIGxvZ2luOiBhc3luYyAoY3JlZGVudGlhbHM6IExvZ2luQ3JlZGVudGlhbHMpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IFN0YXJ0aW5nIGxvZ2luIHByb2Nlc3MnKTtcbiAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlIH0pO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBNYWtpbmcgQVBJIGNhbGwnKTtcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5hdXRoLmxvZ2luKGNyZWRlbnRpYWxzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogQVBJIHJlc3BvbnNlIHJlY2VpdmVkOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICAgIGNvbnN0IHsgdXNlciwgdG9rZW4sIHJvbGUgfSA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBTdG9yZSB0b2tlbiBpbiBsb2NhbFN0b3JhZ2VcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShjb25maWcuYXV0aC50b2tlbktleSwgdG9rZW4pO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBUb2tlbiBzdG9yZWQgaW4gbG9jYWxTdG9yYWdlJyk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gQWRkIHJvbGUgdG8gdXNlciBvYmplY3QgaWYgbm90IHByZXNlbnRcbiAgICAgICAgICBjb25zdCB1c2VyV2l0aFJvbGUgPSB7IC4uLnVzZXIsIHJvbGU6IHVzZXIucm9sZSB8fCByb2xlIH07XG4gICAgICAgICAgXG4gICAgICAgICAgY29uc3QgbmV3U3RhdGUgPSB7XG4gICAgICAgICAgICB1c2VyOiB1c2VyV2l0aFJvbGUsXG4gICAgICAgICAgICB0b2tlbixcbiAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogdHJ1ZSxcbiAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgfTtcbiAgICAgICAgICBcbiAgICAgICAgICBzZXQobmV3U3RhdGUpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBTdGF0ZSB1cGRhdGVkIHN1Y2Nlc3NmdWxseScsIG5ld1N0YXRlKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBGb3JjZSBhIHN0YXRlIGNoZWNrIGFmdGVyIHVwZGF0ZVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRTdGF0ZSA9IGdldCgpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBDdXJyZW50IHN0YXRlIGFmdGVyIHNldDonLCBjdXJyZW50U3RhdGUpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggc3RvcmU6IExvZ2luIGZhaWxlZCcsIGVycm9yKTtcbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IGZhbHNlIH0pO1xuICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICBsb2dvdXQ6ICgpID0+IHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY29uZmlnLmF1dGgudG9rZW5LZXkpO1xuICAgICAgICBzZXQoe1xuICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgdG9rZW46IG51bGwsXG4gICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgIH0sXG5cbiAgICAgIGxvYWRVc2VyOiBhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oY29uZmlnLmF1dGgudG9rZW5LZXkpO1xuICAgICAgICBpZiAoIXRva2VuKSByZXR1cm47XG5cbiAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlIH0pO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmF1dGguZ2V0UHJvZmlsZSgpO1xuICAgICAgICAgIGNvbnN0IHVzZXIgPSByZXNwb25zZS5kYXRhLmRhdGEudXNlcjtcbiAgICAgICAgICBcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgdXNlcixcbiAgICAgICAgICAgIHRva2VuLFxuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgLy8gVG9rZW4gaXMgaW52YWxpZCwgY2xlYXIgaXRcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShjb25maWcuYXV0aC50b2tlbktleSk7XG4gICAgICAgICAgc2V0KHtcbiAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICB0b2tlbjogbnVsbCxcbiAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICBzZXRVc2VyOiAodXNlcjogVXNlciB8IG51bGwpID0+IHtcbiAgICAgICAgc2V0KHsgdXNlciwgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIgfSk7XG4gICAgICB9LFxuXG4gICAgICBzZXRUb2tlbjogKHRva2VuOiBzdHJpbmcgfCBudWxsKSA9PiB7XG4gICAgICAgIHNldCh7IHRva2VuIH0pO1xuICAgICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShjb25maWcuYXV0aC50b2tlbktleSwgdG9rZW4pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGNvbmZpZy5hdXRoLnRva2VuS2V5KTtcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgc2V0TG9hZGluZzogKGxvYWRpbmc6IGJvb2xlYW4pID0+IHtcbiAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiBsb2FkaW5nIH0pO1xuICAgICAgfSxcblxuICAgICAgaW5pdGlhbGl6ZTogYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogSW5pdGlhbGl6aW5nJyk7XG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oY29uZmlnLmF1dGgudG9rZW5LZXkpO1xuICAgICAgICBjb25zdCBzdG9yZWRTdGF0ZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdXRoLXN0b3JlJyk7XG4gICAgICAgIFxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogRm91bmQgdG9rZW46JywgISF0b2tlbik7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBGb3VuZCBzdG9yZWQgc3RhdGU6JywgISFzdG9yZWRTdGF0ZSk7XG4gICAgICAgIFxuICAgICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RvcmU6IE5vIHRva2VuIGZvdW5kIGluIGxvY2FsU3RvcmFnZScpO1xuICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSk7IC8vIE1ha2Ugc3VyZSBsb2FkaW5nIGlzIGZhbHNlXG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBJZiB3ZSBoYXZlIHN0b3JlZCBzdGF0ZSwgdHJ5IHRvIHJlc3RvcmUgaXQgZmlyc3RcbiAgICAgICAgaWYgKHN0b3JlZFN0YXRlKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2Uoc3RvcmVkU3RhdGUpO1xuICAgICAgICAgICAgaWYgKHBhcnNlZC5zdGF0ZSAmJiBwYXJzZWQuc3RhdGUudXNlciAmJiBwYXJzZWQuc3RhdGUuaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBdXRoIHN0b3JlOiBSZXN0b3JpbmcgYXV0aCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZScpO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgLy8gRW5zdXJlIHVzZXIgaGFzIHJvbGUgZmllbGQsIGRlZmF1bHQgdG8gJ21lbWJlcicgaWYgbm90IHByZXNlbnRcbiAgICAgICAgICAgICAgY29uc3QgdXNlcldpdGhSb2xlID0geyBcbiAgICAgICAgICAgICAgICAuLi5wYXJzZWQuc3RhdGUudXNlciwgXG4gICAgICAgICAgICAgICAgcm9sZTogcGFyc2VkLnN0YXRlLnVzZXIucm9sZSB8fCAnbWVtYmVyJyBcbiAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICAgICAgdXNlcjogdXNlcldpdGhSb2xlLFxuICAgICAgICAgICAgICAgIHRva2VuOiBwYXJzZWQuc3RhdGUudG9rZW4gfHwgdG9rZW4sXG4gICAgICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBwYXJzZWQuc3RhdGUuaXNBdXRoZW50aWNhdGVkLFxuICAgICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2VcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQXV0aCBzdG9yZTogRmFpbGVkIHRvIHBhcnNlIHN0b3JlZCBzdGF0ZTonLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBzdG9yZTogVG9rZW4gZm91bmQsIGxvYWRpbmcgdXNlciBwcm9maWxlJyk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlIH0pO1xuICAgICAgICAgIGF3YWl0IHVzZUF1dGhTdG9yZS5nZXRTdGF0ZSgpLmxvYWRVc2VyKCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignQXV0aCBzdG9yZTogRmFpbGVkIHRvIGxvYWQgdXNlciBkdXJpbmcgaW5pdGlhbGl6YXRpb24nLCBlcnJvcik7XG4gICAgICAgICAgLy8gQ2xlYXIgaW52YWxpZCB0b2tlblxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGNvbmZpZy5hdXRoLnRva2VuS2V5KTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aC1zdG9yZScpO1xuICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICB1c2VyOiBudWxsLFxuICAgICAgICAgICAgdG9rZW46IG51bGwsXG4gICAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgIH0pLFxuICAgIHtcbiAgICAgIG5hbWU6ICdhdXRoLXN0b3JlJyxcbiAgICAgIHBhcnRpYWxpemU6IChzdGF0ZSkgPT4gKHtcbiAgICAgICAgdXNlcjogc3RhdGUudXNlcixcbiAgICAgICAgdG9rZW46IHN0YXRlLnRva2VuLFxuICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHN0YXRlLmlzQXV0aGVudGljYXRlZCxcbiAgICAgIH0pLFxuICAgIH1cbiAgKVxuKTsgIl0sIm5hbWVzIjpbImNyZWF0ZSIsInBlcnNpc3QiLCJhcGkiLCJjb25maWciLCJ1c2VBdXRoU3RvcmUiLCJzZXQiLCJnZXQiLCJ1c2VyIiwidG9rZW4iLCJpc0F1dGhlbnRpY2F0ZWQiLCJpc0xvYWRpbmciLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiYXV0aCIsImRhdGEiLCJyb2xlIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsInRva2VuS2V5IiwidXNlcldpdGhSb2xlIiwibmV3U3RhdGUiLCJjdXJyZW50U3RhdGUiLCJlcnJvciIsImxvZ291dCIsInJlbW92ZUl0ZW0iLCJsb2FkVXNlciIsImdldEl0ZW0iLCJnZXRQcm9maWxlIiwic2V0VXNlciIsInNldFRva2VuIiwic2V0TG9hZGluZyIsImxvYWRpbmciLCJpbml0aWFsaXplIiwic3RvcmVkU3RhdGUiLCJwYXJzZWQiLCJKU09OIiwicGFyc2UiLCJzdGF0ZSIsImdldFN0YXRlIiwibmFtZSIsInBhcnRpYWxpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});