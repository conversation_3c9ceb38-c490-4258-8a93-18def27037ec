"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/member/layout",{

/***/ "(app-pages-browser)/./src/app/member/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/member/layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemberLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/member/dashboard',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Chat',\n        href: '/member/chat',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Progress',\n        href: '/member/progress',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'History',\n        href: '/member/history',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/member/profile',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction MemberLayout(param) {\n    let { children } = param;\n    var _user_name;\n    _s();\n    const { user, isAuthenticated, logout, isLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasCheckedAuth, setHasCheckedAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            setIsHydrated(true);\n        }\n    }[\"MemberLayout.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            // Don't redirect during SSR or hydration\n            if (!isHydrated) {\n                console.log('Member layout: Waiting for hydration');\n                return;\n            }\n            // Don't redirect while loading\n            if (isLoading) {\n                console.log('Member layout: Auth is loading');\n                return;\n            }\n            // Mark that we've checked auth at least once\n            setHasCheckedAuth(true);\n            // Check for auth token in localStorage as fallback\n            const token = localStorage.getItem('auth_token');\n            const storedAuthState = localStorage.getItem('auth-store');\n            console.log('Member layout auth check:', {\n                isAuthenticated,\n                user: user ? {\n                    id: user.id,\n                    role: user.role,\n                    name: user.name\n                } : null,\n                hasToken: !!token,\n                hasStoredAuth: !!storedAuthState,\n                pathname,\n                isLoading,\n                isHydrated,\n                hasCheckedAuth\n            });\n            // Debug localStorage content\n            if (token) {\n                console.log('Member layout: Token found:', token.substring(0, 50) + '...');\n            }\n            if (storedAuthState) {\n                try {\n                    var _parsed_state, _parsed_state1, _parsed_state_user, _parsed_state2;\n                    const parsed = JSON.parse(storedAuthState);\n                    console.log('Member layout: Stored auth state:', {\n                        hasUser: !!((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.user),\n                        isAuthenticated: (_parsed_state1 = parsed.state) === null || _parsed_state1 === void 0 ? void 0 : _parsed_state1.isAuthenticated,\n                        userRole: (_parsed_state2 = parsed.state) === null || _parsed_state2 === void 0 ? void 0 : (_parsed_state_user = _parsed_state2.user) === null || _parsed_state_user === void 0 ? void 0 : _parsed_state_user.role\n                    });\n                } catch (e) {\n                    console.log('Member layout: Failed to parse stored auth state');\n                }\n            }\n            // If we have a token but no auth state, give it some time to load\n            if (token && !isAuthenticated && !user) {\n                console.log('Member layout: Have token but no auth state, waiting...');\n                return;\n            }\n            if (!isAuthenticated && !token) {\n                console.log('Member layout: Not authenticated and no token, redirecting to login');\n                window.location.href = '/auth/login';\n                return;\n            }\n            if (isAuthenticated && user && user.role !== 'member') {\n                console.log('Member layout: User role is not member:', user.role);\n                window.location.href = '/auth/login';\n                return;\n            }\n            console.log('Member layout: Auth check passed - user is authenticated member');\n        }\n    }[\"MemberLayout.useEffect\"], [\n        isAuthenticated,\n        user,\n        router,\n        isLoading,\n        isHydrated,\n        pathname\n    ]);\n    const handleLogout = ()=>{\n        logout();\n        window.location.href = '/auth/login';\n    };\n    // Show loading during SSR/hydration or if we haven't checked auth yet\n    if (!isHydrated || isLoading || !hasCheckedAuth && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    // Check for token as fallback if auth state not ready\n    const token = localStorage.getItem('auth_token');\n    if (!isAuthenticated && !token) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 lg:hidden \".concat(sidebarOpen ? 'block' : 'hidden'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-col w-64 bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-16 px-4 bg-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Member Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"text-white hover:text-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 px-4 py-4 space-y-2\",\n                                children: navigation.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: item.href,\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col bg-white shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16 px-4 bg-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"Member Portal\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-4 space-y-2\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden text-gray-500 hover:text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(MemberLayout, \"1aF6hzz87LzvZ0mbESb5nEZRUEo=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = MemberLayout;\nvar _c;\n$RefreshReg$(_c, \"MemberLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/member/layout.tsx\n"));

/***/ })

});