"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/member/layout",{

/***/ "(app-pages-browser)/./src/app/member/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/member/layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemberLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,History,Home,LogOut,Menu,MessageCircle,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/member/dashboard',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Chat',\n        href: '/member/chat',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Progress',\n        href: '/member/progress',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'History',\n        href: '/member/history',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/member/profile',\n        icon: _barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction MemberLayout(param) {\n    let { children } = param;\n    var _user_name;\n    _s();\n    const { user, isAuthenticated, logout, isLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasCheckedAuth, setHasCheckedAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            setIsHydrated(true);\n        }\n    }[\"MemberLayout.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MemberLayout.useEffect\": ()=>{\n            // Don't redirect during SSR or hydration\n            if (!isHydrated) {\n                console.log('Member layout: Waiting for hydration');\n                return;\n            }\n            // Don't redirect while loading\n            if (isLoading) {\n                console.log('Member layout: Auth is loading');\n                return;\n            }\n            // Mark that we've checked auth at least once\n            setHasCheckedAuth(true);\n            // Check for auth token in localStorage as fallback\n            const token = localStorage.getItem('auth_token');\n            const storedAuthState = localStorage.getItem('auth-store');\n            console.log('Member layout auth check:', {\n                isAuthenticated,\n                user: user ? {\n                    id: user.id,\n                    role: user.role,\n                    name: user.name\n                } : null,\n                hasToken: !!token,\n                hasStoredAuth: !!storedAuthState,\n                pathname,\n                isLoading,\n                isHydrated,\n                hasCheckedAuth\n            });\n            // Debug localStorage content\n            if (token) {\n                console.log('Member layout: Token found:', token.substring(0, 50) + '...');\n            }\n            if (storedAuthState) {\n                try {\n                    var _parsed_state, _parsed_state1, _parsed_state_user, _parsed_state2;\n                    const parsed = JSON.parse(storedAuthState);\n                    console.log('Member layout: Stored auth state:', {\n                        hasUser: !!((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.user),\n                        isAuthenticated: (_parsed_state1 = parsed.state) === null || _parsed_state1 === void 0 ? void 0 : _parsed_state1.isAuthenticated,\n                        userRole: (_parsed_state2 = parsed.state) === null || _parsed_state2 === void 0 ? void 0 : (_parsed_state_user = _parsed_state2.user) === null || _parsed_state_user === void 0 ? void 0 : _parsed_state_user.role\n                    });\n                } catch (e) {\n                    console.log('Member layout: Failed to parse stored auth state');\n                }\n            }\n            // If we have a token but no auth state, give it some time to load\n            if (token && !isAuthenticated && !user) {\n                console.log('Member layout: Have token but no auth state, waiting...');\n                return;\n            }\n            if (!isAuthenticated && !token) {\n                console.log('Member layout: Not authenticated and no token, redirecting to login');\n                window.location.href = '/auth/login';\n                return;\n            }\n            // For members, role is always 'member' or might not be present in user object\n            // Members don't have role field in database, it's implied from JWT token\n            const userRole = (user === null || user === void 0 ? void 0 : user.role) || 'member'; // Default to member if no role field\n            if (isAuthenticated && user && userRole !== 'member') {\n                console.log('Member layout: User role is not member:', userRole);\n                window.location.href = '/auth/login';\n                return;\n            }\n            console.log('Member layout: Auth check passed - user is authenticated member');\n        }\n    }[\"MemberLayout.useEffect\"], [\n        isAuthenticated,\n        user,\n        router,\n        isLoading,\n        isHydrated,\n        pathname\n    ]);\n    const handleLogout = ()=>{\n        logout();\n        window.location.href = '/auth/login';\n    };\n    // Show loading during SSR/hydration or if we haven't checked auth yet\n    if (!isHydrated || isLoading || !hasCheckedAuth && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    // Check for token as fallback if auth state not ready\n    const token = localStorage.getItem('auth_token');\n    if (!isAuthenticated && !token) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 lg:hidden \".concat(sidebarOpen ? 'block' : 'hidden'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-col w-64 bg-white shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-16 px-4 bg-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Member Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"text-white hover:text-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex-1 px-4 py-4 space-y-2\",\n                                children: navigation.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: item.href,\n                                        onClick: ()=>setSidebarOpen(false),\n                                        className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-5 w-5 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col bg-white shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16 px-4 bg-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"Member Portal\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-4 space-y-2\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 mr-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-md transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(true),\n                                    className: \"lg:hidden text-gray-500 hover:text-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_History_Home_LogOut_Menu_MessageCircle_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-4\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/meet_trainer/frontend/src/app/member/layout.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(MemberLayout, \"1aF6hzz87LzvZ0mbESb5nEZRUEo=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = MemberLayout;\nvar _c;\n$RefreshReg$(_c, \"MemberLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/member/layout.tsx\n"));

/***/ })

});