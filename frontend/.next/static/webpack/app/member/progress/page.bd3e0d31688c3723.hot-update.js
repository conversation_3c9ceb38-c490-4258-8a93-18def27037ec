"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/member/progress/page",{

/***/ "(app-pages-browser)/./src/hooks/useWebSocket.ts":
/*!***********************************!*\
  !*** ./src/hooks/useWebSocket.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config */ \"(app-pages-browser)/./src/config/index.ts\");\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \n\n\n\nconst useWebSocket = ()=>{\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { user, token } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            if (!user || !token) return;\n            // Initialize socket connection\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(_config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.url, {\n                auth: {\n                    token: token\n                },\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                reconnectionAttempts: _config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.reconnectAttempts,\n                reconnectionDelay: _config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].websocket.reconnectDelay\n            });\n            socketRef.current = socket;\n            // Connection event handlers\n            socket.on('connect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    console.log('WebSocket connected');\n                    setIsConnected(true);\n                    // Send authentication data after connection\n                    if (user && token) {\n                        socket.emit('authenticate', {\n                            token: token,\n                            userId: user.email,\n                            role: user.role || 'member'\n                        });\n                    }\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('disconnect', {\n                \"useWebSocket.useEffect\": ()=>{\n                    console.log('WebSocket disconnected');\n                    setIsConnected(false);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('connect_error', {\n                \"useWebSocket.useEffect\": (error)=>{\n                    console.error('WebSocket connection error:', error);\n                    setIsConnected(false);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Authentication response handlers\n            socket.on('authenticated', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    console.log('WebSocket authenticated successfully:', data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            socket.on('authentication-error', {\n                \"useWebSocket.useEffect\": (data)=>{\n                    console.error('WebSocket authentication failed:', data);\n                }\n            }[\"useWebSocket.useEffect\"]);\n            // Cleanup on unmount\n            return ({\n                \"useWebSocket.useEffect\": ()=>{\n                    socket.disconnect();\n                    socketRef.current = null;\n                    setIsConnected(false);\n                }\n            })[\"useWebSocket.useEffect\"];\n        }\n    }[\"useWebSocket.useEffect\"], [\n        user,\n        token\n    ]);\n    const emit = (event, data)=>{\n        if (socketRef.current && isConnected) {\n            socketRef.current.emit(event, data);\n        }\n    };\n    const on = (event, callback)=>{\n        if (socketRef.current) {\n            socketRef.current.on(event, callback);\n        }\n    };\n    const off = (event, callback)=>{\n        if (socketRef.current) {\n            if (callback) {\n                socketRef.current.off(event, callback);\n            } else {\n                socketRef.current.off(event);\n            }\n        }\n    };\n    return {\n        socket: socketRef.current,\n        isConnected,\n        emit,\n        on,\n        off\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWebSocket.ts\n"));

/***/ })

});