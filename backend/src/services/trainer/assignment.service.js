const { Trainer, Member, TrainerScore, TrainerM<PERSON>ber, sequelize } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

/**
 * Assign trainer to members
 * @param {number} trainerId
 * @param {Array} memberIds
 * @param {number} adminId
 * @param {string} adminRole - Admin role (admin, super_admin)
 * @returns {Promise<Object>}
 */
async function assignTrainerToMembers(trainerId, memberIds, adminId, adminRole = 'admin') {
  const transaction = await sequelize.transaction();
  try {
    logger.info(`Assignment request: trainerId=${trainerId}, memberIds=${JSON.stringify(memberIds)}, adminId=${adminId}, adminRole=${adminRole}`);
    
    // Super admin can assign any active trainer, regular admin can only assign their own trainers
    const whereClause = { id: trainerId, status: 'active' };
    if (adminRole !== 'super_admin') {
      whereClause.adminId = adminId;
    }
    
    logger.info(`Trainer query where clause: ${JSON.stringify(whereClause)}`);
    
    const trainer = await Trainer.findOne({ where: whereClause });
    logger.info(`Trainer found: ${trainer ? `Yes (ID: ${trainer.id}, name: ${trainer.name}, adminId: ${trainer.adminId})` : 'No'}`);
    
    if (!trainer) throw new Error('Trainer not found, access denied, or trainer is inactive');
    
    const members = await Member.findAll({ where: { id: { [Op.in]: memberIds }, isActive: true } });
    logger.info(`Members found: ${members.length}/${memberIds.length} - IDs: ${members.map(m => `${m.id}(${m.name})`).join(', ')}`);
    
    if (members.length !== memberIds.length) throw new Error('One or more members not found or inactive');
    
    // Check for existing assignments
    const existingAssignments = await TrainerMember.findAll({
      where: { 
        trainerId, 
        memberId: { [Op.in]: memberIds } 
      }
    });
    logger.info(`Existing assignments: ${existingAssignments.length} - IDs: ${existingAssignments.map(a => a.memberId).join(', ')}`);
    
    // Filter out already assigned members
    const existingMemberIds = existingAssignments.map(a => a.memberId);
    const newMembers = members.filter(m => !existingMemberIds.includes(m.id));
    
    if (newMembers.length === 0) {
      throw new Error('All selected members are already assigned to this trainer');
    }
    
    // Create assignments manually to include assignedBy field
    const assignmentPromises = newMembers.map(member => 
      TrainerMember.create({
        trainerId,
        memberId: member.id,
        assignedBy: adminId,
        assignedAt: new Date()
      }, { transaction })
    );
    
    await Promise.all(assignmentPromises);
    logger.info(`Created ${newMembers.length} new trainer assignments`);
    
    // Create initial scores for new assignments
    const scorePromises = newMembers.map(member => TrainerScore.findOrCreate({
      where: { trainerId, memberId: member.id },
      defaults: { trainerId, memberId: member.id, assignedBy: adminId, progress: 0, score: null, notes: 'Initial assignment' },
      transaction
    }));
    await Promise.all(scorePromises);
    
    await transaction.commit();
    logger.info(`Trainer ${trainerId} assigned to ${newMembers.length} new members by admin ${adminId}`);
    
    return { 
      success: true, 
      message: `Trainer assigned to ${newMembers.length} new members successfully`, 
      assignments: newMembers.map(m => m.id),
      skipped: existingMemberIds.length > 0 ? existingMemberIds : []
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error assigning trainer to members:', {
      error: error.message,
      stack: error.stack,
      trainerId,
      memberIds,
      adminId,
      adminRole
    });
    return { success: false, error: error.message };
  }
}

/**
 * Remove trainer assignment from members
 * @param {number} trainerId
 * @param {Array} memberIds
 * @param {number} adminId
 * @param {string} adminRole - Admin role (admin, super_admin)
 * @returns {Promise<Object>}
 */
async function removeTrainerFromMembers(trainerId, memberIds, adminId, adminRole = 'admin') {
  const transaction = await sequelize.transaction();
  try {
    // Super admin can unassign any trainer, regular admin can only unassign their own trainers
    const whereClause = { id: trainerId };
    if (adminRole !== 'super_admin') {
      whereClause.adminId = adminId;
    }
    
    const trainer = await Trainer.findOne({ where: whereClause });
    if (!trainer) throw new Error('Trainer not found or access denied');
    
    const members = await Member.findAll({ where: { id: { [Op.in]: memberIds } } });
    if (members.length !== memberIds.length) throw new Error('One or more members not found');
    
    // Remove assignments manually
    await TrainerMember.destroy({
      where: {
        trainerId,
        memberId: { [Op.in]: memberIds }
      },
      transaction
    });
    
    await transaction.commit();
    logger.info(`Trainer ${trainerId} removed from ${memberIds.length} members by admin ${adminId}`);
    return { success: true, message: `Trainer removed from ${members.length} members successfully` };
  } catch (error) {
    await transaction.rollback();
    logger.error('Error removing trainer from members:', error);
    return { success: false, error: error.message };
  }
}

module.exports = { assignTrainerToMembers, removeTrainerFromMembers }; 