const authService = require('../services/auth.service');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, AuthenticationError, asyncHandler } = require('../middleware/error.middleware');

class AuthController {
  /**
   * Login endpoint for both admins and members
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async login(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { email, password, userType = 'member' } = req.body;

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_CREDENTIALS',
            message: 'Email and password are required'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Validate userType
      if (!['admin', 'member'].includes(userType)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_USER_TYPE',
            message: 'User type must be either admin or member'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Login attempt for ${email} as ${userType}`);

      const result = await authService.authenticateUser(email, password, userType);

      if (!result.success) {
        loggerWithId.warn(`Login failed for ${email}: ${result.error}`);
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: 'Invalid credentials'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Login successful for ${email} as ${result.role}`);

      res.json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          role: result.role
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGIN_ERROR',
          message: 'An error occurred during login'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Register new admin (super admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async registerAdmin(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { name, email, password, role = 'admin' } = req.body;

      // Validate input
      if (!name || !email || !password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_FIELDS',
            message: 'Name, email, and password are required'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Validate role
      if (!['admin', 'super_admin'].includes(role)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ROLE',
            message: 'Role must be either admin or super_admin'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Only super admin can create super admin
      if (role === 'super_admin' && req.user.role !== 'super_admin') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Only super admin can create super admin accounts'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Admin registration attempt for ${email} with role ${role}`);

      const result = await authService.createAdmin({ name, email, password, role });

      if (!result.success) {
        loggerWithId.warn(`Admin registration failed for ${email}: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'REGISTRATION_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Admin registration successful for ${email}`);

      res.status(201).json({
        success: true,
        data: {
          admin: result.admin
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Admin registration error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'REGISTRATION_ERROR',
          message: 'An error occurred during admin registration'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Register new member (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async registerMember(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { name, email, password } = req.body;
      const adminId = req.user.id; // Get admin ID from authenticated user

      // Validate input
      if (!name || !email || !password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_FIELDS',
            message: 'Name, email, and password are required'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Member registration attempt for ${email} by admin ${adminId}`);

      const result = await authService.createMember({ name, email, password }, adminId);

      if (!result.success) {
        loggerWithId.warn(`Member registration failed for ${email}: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'REGISTRATION_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Member registration successful for ${email} by admin ${adminId}`);

      res.status(201).json({
        success: true,
        data: {
          member: result.member
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Member registration error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'REGISTRATION_ERROR',
          message: 'An error occurred during member registration'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Change password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async changePassword(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { oldPassword, newPassword } = req.body;
      const userId = req.user.id;
      const userType = req.user.role === 'member' ? 'member' : 'admin';

      // Validate input
      if (!oldPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_PASSWORDS',
            message: 'Old password and new password are required'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Validate new password length
      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'WEAK_PASSWORD',
            message: 'New password must be at least 6 characters long'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Password change attempt for user ${userId}`);

      const result = await authService.changePassword(userId, userType, oldPassword, newPassword);

      if (!result.success) {
        loggerWithId.warn(`Password change failed for user ${userId}: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'PASSWORD_CHANGE_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Password change successful for user ${userId}`);

      res.json({
        success: true,
        data: {
          message: result.message
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Password change error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PASSWORD_CHANGE_ERROR',
          message: 'An error occurred during password change'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Get current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getProfile(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const userId = req.user.id;
      const userType = req.user.role === 'member' ? 'member' : 'admin';

      loggerWithId.info(`Profile request for user ${userId}`);

      const result = await authService.getUserById(userId, userType);

      if (!result.success) {
        loggerWithId.warn(`Profile fetch failed for user ${userId}: ${result.error}`);
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      res.json({
        success: true,
        data: {
          user: result.user
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Profile fetch error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PROFILE_FETCH_ERROR',
          message: 'An error occurred while fetching profile'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Logout endpoint (client-side token invalidation)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async logout(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const userId = req.user.id;
      const userEmail = req.user.email;

      loggerWithId.info(`Logout request for user ${userEmail}`);

      // In a more sophisticated implementation, you might want to:
      // 1. Add token to a blacklist in Redis
      // 2. Store token expiration time
      // 3. Implement token refresh mechanism

      res.json({
        success: true,
        data: {
          message: 'Logged out successfully'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGOUT_ERROR',
          message: 'An error occurred during logout'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }
}

// Hapus semua fungsi yang sudah dipindah ke controller modular
// Ekspor hanya fungsi yang tersisa (jika ada)

// Jika tidak ada, kosongkan ekspor
module.exports = {}; 