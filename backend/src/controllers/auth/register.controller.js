const authService = require('../../services/auth.service');
const logger = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');

/**
 * Register new admin (super admin only)
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @returns {Promise<void>}
 */
async function registerAdmin(req, res) {
  const correlationId = uuidv4();
  const loggerWithId = logger.addCorrelationId(correlationId);
  try {
    const { name, email, password, role = 'admin' } = req.body;
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_FIELDS',
          message: 'Name, email, and password are required'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    if (!['admin', 'super_admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ROLE',
          message: 'Role must be either admin or super_admin'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    if (role === 'super_admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Only super admin can create super admin accounts'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    loggerWithId.info(`Admin registration attempt for ${email} with role ${role}`);
    const result = await authService.createAdmin({ name, email, password, role });
    if (!result.success) {
      loggerWithId.warn(`Admin registration failed for ${email}: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: result.error
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    loggerWithId.info(`Admin registration successful for ${email}`);
    res.status(201).json({
      success: true,
      data: {
        admin: result.admin
      },
      timestamp: new Date().toISOString(),
      correlationId
    });
  } catch (error) {
    loggerWithId.error('Admin registration error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'REGISTRATION_ERROR',
        message: 'An error occurred during admin registration'
      },
      timestamp: new Date().toISOString(),
      correlationId
    });
  }
}

/**
 * Register new member (admin only)
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @returns {Promise<void>}
 */
async function registerMember(req, res) {
  const correlationId = uuidv4();
  const loggerWithId = logger.addCorrelationId(correlationId);
  try {
    const { name, email, password } = req.body;
    const adminId = req.user.id; // Get admin ID from authenticated user
    
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_FIELDS',
          message: 'Name, email, and password are required'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    
    loggerWithId.info(`Member registration attempt for ${email} by admin ${adminId}`);
    
    const result = await authService.createMember({ name, email, password }, adminId);
    
    if (!result.success) {
      loggerWithId.warn(`Member registration failed for ${email}: ${result.error}`);
      return res.status(400).json({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: result.error
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
    
    loggerWithId.info(`Member registration successful for ${email} by admin ${adminId}`);
    
    res.status(201).json({
      success: true,
      data: {
        member: result.member
      },
      timestamp: new Date().toISOString(),
      correlationId
    });
  } catch (error) {
    loggerWithId.error('Member registration error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'REGISTRATION_ERROR',
        message: 'An error occurred during member registration'
      },
      timestamp: new Date().toISOString(),
      correlationId
    });
  }
}

module.exports = {
  registerAdmin,
  registerMember
}; 