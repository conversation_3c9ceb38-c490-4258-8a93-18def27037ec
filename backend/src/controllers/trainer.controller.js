const TrainerService = require('../services/trainer.service');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

const trainerService = new TrainerService();

class TrainerController {
  /**
   * Create a new trainer
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async createTrainer(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const adminId = req.user.id;
      const trainerData = req.body;

      loggerWithId.info(`Creating trainer: ${trainerData.name} by admin ${adminId}`);

      const result = await trainerService.createTrainer(trainerData, adminId);

      if (!result.success) {
        loggerWithId.warn(`Trainer creation failed: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'TRAINER_CREATION_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Trainer created successfully: ${result.trainer.id}`);

      res.status(201).json({
        success: true,
        data: {
          trainer: result.trainer
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Create trainer error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TRAINER_CREATION_ERROR',
          message: 'An error occurred while creating the trainer'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Get trainer by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getTrainer(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;
      const adminId = req.user.id;

      loggerWithId.info(`Getting trainer: ${id} for admin ${adminId}`);

      const result = await trainerService.getTrainerById(parseInt(id), adminId);

      if (!result.success) {
        loggerWithId.warn(`Trainer fetch failed: ${result.error}`);
        return res.status(404).json({
          success: false,
          error: {
            code: 'TRAINER_NOT_FOUND',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      res.json({
        success: true,
        data: {
          trainer: result.trainer
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Get trainer error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TRAINER_FETCH_ERROR',
          message: 'An error occurred while fetching the trainer'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Get all trainers for admin
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getTrainers(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const adminId = req.user.id;
      const adminRole = req.user.role;
      const options = req.query;

      loggerWithId.info(`Getting trainers for ${adminRole} ${adminId}`);

      const result = await trainerService.getTrainersByAdmin(adminId, options, adminRole);

      if (!result.success) {
        loggerWithId.warn(`Trainers fetch failed: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'TRAINERS_FETCH_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      res.json({
        success: true,
        data: {
          trainers: result.trainers,
          pagination: result.pagination
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Get trainers error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TRAINERS_FETCH_ERROR',
          message: 'An error occurred while fetching trainers'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Update trainer
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateTrainer(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;
      const adminId = req.user.id;
      const updateData = req.body;

      loggerWithId.info(`Updating trainer: ${id} by admin ${adminId}`);

      const result = await trainerService.updateTrainer(parseInt(id), updateData, adminId);

      if (!result.success) {
        loggerWithId.warn(`Trainer update failed: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'TRAINER_UPDATE_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Trainer updated successfully: ${id}`);

      res.json({
        success: true,
        data: {
          trainer: result.trainer
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Update trainer error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TRAINER_UPDATE_ERROR',
          message: 'An error occurred while updating the trainer'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Delete trainer
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteTrainer(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;
      const adminId = req.user.id;

      loggerWithId.info(`Deleting trainer: ${id} by admin ${adminId}`);

      const result = await trainerService.deleteTrainer(parseInt(id), adminId);

      if (!result.success) {
        loggerWithId.warn(`Trainer deletion failed: ${result.error}`);
        return res.status(400).json({
          success: false,
          error: {
            code: 'TRAINER_DELETE_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Trainer deleted successfully: ${id}`);

      res.json({
        success: true,
        data: {
          message: result.message
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Delete trainer error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TRAINER_DELETE_ERROR',
          message: 'An error occurred while deleting the trainer'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Get trainers for a member
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getMemberTrainers(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const memberId = req.user.role === 'member' ? req.user.id : parseInt(req.params.memberId);

      loggerWithId.info(`Getting trainers for member ${memberId}`);

      const result = await trainerService.getTrainersByMember(memberId);

      if (!result.success) {
        loggerWithId.warn(`Member trainers fetch failed: ${result.error}`);
        return res.status(404).json({
          success: false,
          error: {
            code: 'MEMBER_TRAINERS_FETCH_FAILED',
            message: result.error
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      res.json({
        success: true,
        data: {
          trainers: result.trainers
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Get member trainers error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'MEMBER_TRAINERS_FETCH_ERROR',
          message: 'An error occurred while fetching member trainers'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Validate system prompt
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async validateSystemPrompt(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { systemPrompt } = req.body;

      if (!systemPrompt) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_SYSTEM_PROMPT',
            message: 'System prompt is required'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info('Validating system prompt');

      const validation = trainerService.validateSystemPrompt(systemPrompt);

      res.json({
        success: true,
        data: {
          validation
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Validate system prompt error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'An error occurred while validating the system prompt'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }
}

module.exports = new TrainerController(); 