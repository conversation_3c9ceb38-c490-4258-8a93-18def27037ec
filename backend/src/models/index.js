const { Sequelize } = require('sequelize');
const config = require('../config/database');

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Initialize Sequelize
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool
  }
);

// Import models
const Admin = require('./admin')(sequelize, Sequelize.DataTypes);
const Member = require('./member')(sequelize, Sequelize.DataTypes);
const AdminMember = require('./admin-member')(sequelize, Sequelize.DataTypes);
const Trainer = require('./trainer')(sequelize, Sequelize.DataTypes);
const TrainerSubmodule = require('./trainer-submodule')(sequelize, Sequelize.DataTypes);
const TrainerMember = require('./trainer-member')(sequelize, Sequelize.DataTypes);
const TrainerConversation = require('./trainer-conversation')(sequelize, Sequelize.DataTypes);
const TrainerScore = require('./trainer-score')(sequelize, Sequelize.DataTypes);
const OpenAIThread = require('./openai-thread')(sequelize, Sequelize.DataTypes);
const OpenAIMessage = require('./openai-message')(sequelize, Sequelize.DataTypes);

// Define associations
const db = {
  Admin,
  Member,
  AdminMember,
  Trainer,
  TrainerSubmodule,
  TrainerMember,
  TrainerConversation,
  TrainerScore,
  OpenAIThread,
  OpenAIMessage,
  sequelize,
  Sequelize
};

// Set up associations
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

module.exports = db; 