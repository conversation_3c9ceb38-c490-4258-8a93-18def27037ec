const request = require('supertest');
const { app } = require('../../../server');
const { Admin, Member, Trainer } = require('../../../src/models');
const bcrypt = require('bcrypt');

describe('Trainers Integration Tests', () => {
  let testAdmin;
  let testMember;
  let adminToken;
  let memberToken;

  beforeEach(async () => {
    // Create test admin
    testAdmin = await Admin.create({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 12),
      role: 'admin'
    });

    // Create test member
    testMember = await Member.create({
      name: 'Test Member',
      email: '<EMAIL>',
      password: await bcrypt.hash('password123', 12)
    });

    // Get admin token
    const adminLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        userType: 'admin'
      });
    adminToken = adminLoginResponse.body.data.token;

    // Get member token
    const memberLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        userType: 'member'
      });
    memberToken = memberLoginResponse.body.data.token;
  });

  describe('POST /api/trainers', () => {
    test('should create trainer with valid data', async () => {
      const response = await request(app)
        .post('/api/trainers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'JavaScript Fundamentals',
          systemPrompt: 'You are a helpful JavaScript tutor that teaches programming concepts step by step.'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trainer.name).toBe('JavaScript Fundamentals');
      expect(response.body.data.trainer.systemPrompt).toBe('You are a helpful JavaScript tutor that teaches programming concepts step by step.');
      expect(response.body.data.trainer.status).toBe('active');
      expect(response.body.data.trainer.adminId).toBe(testAdmin.id);
    });

    test('should reject trainer creation without authentication', async () => {
      const response = await request(app)
        .post('/api/trainers')
        .send({
          name: 'JavaScript Fundamentals',
          systemPrompt: 'You are a helpful JavaScript tutor.'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('should reject trainer creation by member', async () => {
      const response = await request(app)
        .post('/api/trainers')
        .set('Authorization', `Bearer ${memberToken}`)
        .send({
          name: 'JavaScript Fundamentals',
          systemPrompt: 'You are a helpful JavaScript tutor.'
        });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
    });

    test('should reject invalid trainer data', async () => {
      const response = await request(app)
        .post('/api/trainers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: '',
          systemPrompt: 'You are a helpful JavaScript tutor.'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should create trainer without system prompt', async () => {
      const response = await request(app)
        .post('/api/trainers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'JavaScript Fundamentals'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trainer.name).toBe('JavaScript Fundamentals');
      expect(response.body.data.trainer.systemPrompt).toBeNull();
      expect(response.body.data.trainer.status).toBe('active');
      expect(response.body.data.trainer.adminId).toBe(testAdmin.id);
    });
  });

  describe('GET /api/trainers', () => {
    beforeEach(async () => {
      // Create test trainers
      await Trainer.create({
        name: 'Trainer 1',
        systemPrompt: 'You are trainer 1',
        status: 'active',
        adminId: testAdmin.id
      });

      await Trainer.create({
        name: 'Trainer 2',
        systemPrompt: 'You are trainer 2',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should get trainers for admin', async () => {
      const response = await request(app)
        .get('/api/trainers')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.length).toBe(2);
      expect(response.body.data[0].name).toBe('Trainer 1');
      expect(response.body.data[1].name).toBe('Trainer 2');
    });

    test('should require authentication', async () => {
      const response = await request(app)
        .get('/api/trainers');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/trainers/:id', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should get trainer by id', async () => {
      const response = await request(app)
        .get(`/api/trainers/${testTrainer.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Test Trainer');
      expect(response.body.data.systemPrompt).toBe('You are a test trainer');
    });

    test('should return 404 for non-existent trainer', async () => {
      const response = await request(app)
        .get('/api/trainers/999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/trainers/:id', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should update trainer', async () => {
      const response = await request(app)
        .put(`/api/trainers/${testTrainer.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Updated Trainer',
          systemPrompt: 'You are an updated trainer'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Updated Trainer');
      expect(response.body.data.systemPrompt).toBe('You are an updated trainer');
    });

    test('should return 404 for non-existent trainer', async () => {
      const response = await request(app)
        .put('/api/trainers/999')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Updated Trainer'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/trainers/:id', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should delete trainer', async () => {
      const response = await request(app)
        .delete(`/api/trainers/${testTrainer.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Trainer deleted successfully');

      // Verify trainer is deleted
      const deletedTrainer = await Trainer.findByPk(testTrainer.id);
      expect(deletedTrainer).toBeNull();
    });

    test('should return 404 for non-existent trainer', async () => {
      const response = await request(app)
        .delete('/api/trainers/999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/trainers/:id/assign', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should assign trainer to member', async () => {
      const response = await request(app)
        .post(`/api/trainers/${testTrainer.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          memberId: testMember.id
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Trainer assigned to member successfully');
    });

    test('should reject assignment to non-existent member', async () => {
      const response = await request(app)
        .post(`/api/trainers/${testTrainer.id}/assign`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          memberId: 999
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should reject assignment of non-existent trainer', async () => {
      const response = await request(app)
        .post('/api/trainers/999/assign')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          memberId: testMember.id
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/trainers/:id/assign/:memberId', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });

      // Assign trainer to member
      await testTrainer.addMember(testMember, {
        through: { assignedBy: testAdmin.id }
      });
    });

    test('should unassign trainer from member', async () => {
      const response = await request(app)
        .delete(`/api/trainers/${testTrainer.id}/assign/${testMember.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Trainer unassigned from member successfully');
    });

    test('should return 404 for non-existent assignment', async () => {
      const response = await request(app)
        .delete(`/api/trainers/${testTrainer.id}/assign/999`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/trainers/:id/statistics', () => {
    let testTrainer;

    beforeEach(async () => {
      testTrainer = await Trainer.create({
        name: 'Test Trainer',
        systemPrompt: 'You are a test trainer',
        status: 'active',
        adminId: testAdmin.id
      });
    });

    test('should get trainer statistics', async () => {
      const response = await request(app)
        .get(`/api/trainers/${testTrainer.id}/statistics`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.trainerId).toBe(testTrainer.id);
      expect(response.body.data.trainerName).toBe('Test Trainer');
      expect(response.body.data).toHaveProperty('totalMembers');
      expect(response.body.data).toHaveProperty('totalConversations');
      expect(response.body.data).toHaveProperty('averageProgress');
      expect(response.body.data).toHaveProperty('averageScore');
    });
  });
}); 