'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Make systemPrompt column nullable in trainers table
    await queryInterface.changeColumn('trainers', 'systemPrompt', {
      type: Sequelize.TEXT,
      allowNull: true
    });
  },

  async down (queryInterface, Sequelize) {
    // Revert systemPrompt column to NOT NULL
    await queryInterface.changeColumn('trainers', 'systemPrompt', {
      type: Sequelize.TEXT,
      allowNull: false
    });
  }
};
